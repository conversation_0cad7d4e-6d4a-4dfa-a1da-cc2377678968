/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.helper;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fulfillmen.shop.common.context.OrderContextDTO;
import com.fulfillmen.shop.domain.dto.order.OrderItemInfo;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum;
import com.fulfillmen.shop.domain.req.OrderReq.CreateOrderSubmitReq;
import com.fulfillmen.shop.domain.vo.OrderPreviewVO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * OrderContextHelper 价格计算测试类 使用 Mock 数据验证价格计算逻辑的正确性
 */
class OrderContextHelperPriceTest {

    @Test
    @DisplayName("价格计算完整性测试 - 验证运费分配和价格一致性问题")
    void testPriceCalculationWithFreightAllocation() {
        // Given: 构造测试数据
        MockTestData mockData = createMockTestData();

        // When: 使用测试专用的价格计算逻辑
        OrderContextDTO result = buildContextForTest(mockData);

        // Then: 验证价格计算结果
        verifyPriceCalculation(mockData, result);
    }

    @Test
    @DisplayName("运费分配逻辑测试 - 当前逻辑 vs 正确逻辑对比")
    void testFreightAllocationLogic() {
        // Given
        BigDecimal totalFreight = new BigDecimal("30.00");
        BigDecimal supplierA_goods = new BigDecimal("180.00");
        BigDecimal supplierB_goods = new BigDecimal("360.00");
        BigDecimal totalGoods = supplierA_goods.add(supplierB_goods);

        // 正确的运费分配逻辑
        BigDecimal expectedSupplierA_freight = totalFreight
            .multiply(supplierA_goods)
            .divide(totalGoods, 2, RoundingMode.HALF_UP); // 10.00
        BigDecimal expectedSupplierB_freight = totalFreight
            .multiply(supplierB_goods)
            .divide(totalGoods, 2, RoundingMode.HALF_UP); // 20.00

        // 验证运费分配比例
        assertEquals(new BigDecimal("10.00"), expectedSupplierA_freight);
        assertEquals(new BigDecimal("20.00"), expectedSupplierB_freight);
        assertEquals(totalFreight, expectedSupplierA_freight.add(expectedSupplierB_freight));

        System.out.println("✅ 运费分配验证通过:");
        System.out.println("  供应商A运费: " + expectedSupplierA_freight + " (占比33.33%)");
        System.out.println("  供应商B运费: " + expectedSupplierB_freight + " (占比66.67%)");
        System.out.println("  总运费: " + totalFreight);
    }

    /**
     * 创建 Mock 测试数据
     */
    private MockTestData createMockTestData() {
        MockTestData data = new MockTestData();

        // 基础参数
        data.userId = 12345L;
        data.totalFreight = new BigDecimal("30.00"); // 总运费30元
        data.serviceFeeRate = new BigDecimal("0.15"); // 15%服务费
        data.shoppingCartIds = Arrays.asList(1L, 2L, 3L);

        // Mock 供应商A的商品
        TzProductSpu spuA = mockSpu(100L, "supplier_A", "供应商A");
        TzProductSku skuA1 = mockSku(1001L, 100L, new BigDecimal("50.00"));
        TzProductSku skuA2 = mockSku(1002L, 100L, new BigDecimal("80.00"));

        // Mock 供应商B的商品
        TzProductSpu spuB = mockSpu(200L, "supplier_B", "供应商B");
        TzProductSku skuB1 = mockSku(2001L, 200L, new BigDecimal("120.00"));

        // 商品数量映射
        data.skuQuantityMap = Map.of(
            1001L, 2, // 商品A1: 50×2 = 100元
            1002L, 1, // 商品A2: 80×1 = 80元
            2001L, 3 // 商品B1: 120×3 = 360元
        );
        // 总商品金额: 540元

        data.productSkuList = Arrays.asList(skuA1, skuA2, skuB1);
        data.spuMap = Map.of(100L, spuA, 200L, spuB);

        // Mock 订单预览VO
        data.previewVO = mockPreviewVO(data.totalFreight);

        // Mock 订单提交请求
        data.orderSubmitReq = new CreateOrderSubmitReq();
        data.orderSubmitReq.setBuyerMessage("Mock 测试订单");

        return data;
    }

    /**
     * 验证价格计算结果
     */
    private void verifyPriceCalculation(MockTestData mockData, OrderContextDTO result) {
        TzOrderPurchase purchase = result.getPurchaseOrder();
        List<TzOrderSupplier> suppliers = result.getSupplierOrders();

        // 预期计算结果
        BigDecimal expectedTotalGoods = new BigDecimal("540.00");
        BigDecimal expectedTotalServiceFee = expectedTotalGoods.multiply(mockData.serviceFeeRate)
            .setScale(2, RoundingMode.HALF_UP); // 81.00
        BigDecimal expectedPurchaseTotal = expectedTotalGoods
            .add(mockData.totalFreight)
            .add(expectedTotalServiceFee); // 651.00

        System.out.println("\n========== 价格计算验证结果 ==========");

        // 1. 验证采购单价格
        assertEquals(expectedTotalGoods, purchase.getCustomerGoodsAmount(), "采购单商品金额");
        assertEquals(mockData.totalFreight, purchase.getCustomerTotalFreight(), "采购单运费");
        assertEquals(expectedTotalServiceFee, purchase.getServiceFee(), "采购单服务费");
        assertEquals(expectedPurchaseTotal, purchase.getCustomerTotalAmount(), "采购单总金额");

        System.out.println("✅ 采购单价格验证通过:");
        System.out.println("  商品金额: " + purchase.getCustomerGoodsAmount());
        System.out.println("  运费: " + purchase.getCustomerTotalFreight());
        System.out.println("  服务费: " + purchase.getServiceFee());
        System.out.println("  总金额: " + purchase.getCustomerTotalAmount());

        // 2. 验证供应商订单价格
        suppliers.sort(Comparator.comparing(TzOrderSupplier::getSupplierId));

        // 供应商A (supplier_A)
        TzOrderSupplier supplierA = suppliers.get(0);
        BigDecimal supplierA_expectedGoods = new BigDecimal("180.00"); // 100+80
        BigDecimal supplierA_expectedServiceFee = supplierA_expectedGoods.multiply(mockData.serviceFeeRate)
            .setScale(2, RoundingMode.HALF_UP); // 27.00

        assertEquals(supplierA_expectedGoods, supplierA.getCustomerGoodsAmount(), "供应商A商品金额");
        assertEquals(supplierA_expectedServiceFee, supplierA.getServiceFee(), "供应商A服务费");

        // 供应商B (supplier_B)
        TzOrderSupplier supplierB = suppliers.get(1);
        BigDecimal supplierB_expectedGoods = new BigDecimal("360.00");
        BigDecimal supplierB_expectedServiceFee = supplierB_expectedGoods.multiply(mockData.serviceFeeRate)
            .setScale(2, RoundingMode.HALF_UP); // 54.00

        assertEquals(supplierB_expectedGoods, supplierB.getCustomerGoodsAmount(), "供应商B商品金额");
        assertEquals(supplierB_expectedServiceFee, supplierB.getServiceFee(), "供应商B服务费");

        System.out.println("✅ 供应商订单商品金额和服务费验证通过");

        // 3. 【关键问题验证】运费分配问题
        BigDecimal supplierA_actualFreight = supplierA.getCustomerFreightAmount();
        BigDecimal supplierB_actualFreight = supplierB.getCustomerFreightAmount();

        System.out.println("\n❌ 运费分配问题验证:");
        System.out.println("  供应商A实际运费: " + supplierA_actualFreight + " (预期应该是10.00)");
        System.out.println("  供应商B实际运费: " + supplierB_actualFreight + " (预期应该是20.00)");

        // 当前代码的运费都是0，这就是问题所在
        assertEquals(BigDecimal.ZERO, supplierA_actualFreight, "当前代码中供应商A运费为0");
        assertEquals(BigDecimal.ZERO, supplierB_actualFreight, "当前代码中供应商B运费为0");

        // 4. 【关键问题验证】价格一致性问题
        BigDecimal suppliersActualTotal = suppliers.stream()
            .map(TzOrderSupplier::getCustomerTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        System.out.println("\n❌ 价格一致性问题验证:");
        System.out.println("  采购单总金额: " + purchase.getCustomerTotalAmount());
        System.out.println("  供应商订单总计: " + suppliersActualTotal);
        System.out.println("  差额: " + purchase.getCustomerTotalAmount().subtract(suppliersActualTotal));

        // 预期差额是30元运费
        BigDecimal expectedDifference = new BigDecimal("30.00");
        BigDecimal actualDifference = purchase.getCustomerTotalAmount().subtract(suppliersActualTotal);
        assertEquals(expectedDifference, actualDifference, "价格不一致差额应该等于总运费");

        // 5. 打印正确的运费分配逻辑
        System.out.println("\n💡 正确的运费分配应该是:");
        BigDecimal correctSupplierA_freight = mockData.totalFreight
            .multiply(supplierA_expectedGoods)
            .divide(expectedTotalGoods, 2, RoundingMode.HALF_UP); // 10.00
        BigDecimal correctSupplierB_freight = mockData.totalFreight
            .multiply(supplierB_expectedGoods)
            .divide(expectedTotalGoods, 2, RoundingMode.HALF_UP); // 20.00

        System.out.println("  供应商A运费: " + correctSupplierA_freight);
        System.out.println("  供应商B运费: " + correctSupplierB_freight);
        System.out.println("  运费合计: " + correctSupplierA_freight.add(correctSupplierB_freight));

        BigDecimal correctSupplierA_total = supplierA_expectedGoods
            .add(correctSupplierA_freight)
            .add(supplierA_expectedServiceFee); // 217.00
        BigDecimal correctSupplierB_total = supplierB_expectedGoods
            .add(correctSupplierB_freight)
            .add(supplierB_expectedServiceFee); // 434.00

        System.out.println("  供应商A正确总金额: " + correctSupplierA_total);
        System.out.println("  供应商B正确总金额: " + correctSupplierB_total);
        System.out.println("  正确的供应商总计: " + correctSupplierA_total.add(correctSupplierB_total));
    }

    // ========== Mock 数据创建辅助方法 ==========

    private TzProductSpu mockSpu(Long id, String supplierId, String supplierName) {
        TzProductSpu spu = new TzProductSpu();
        spu.setId(id);
        spu.setSourcePlatformSellerOpenId(supplierId);
        spu.setSourcePlatformSellerName(supplierName);
        spu.setMinOrderQuantity(1);
        spu.setIsSingleItem(TzProductSpuSingleItemEnum.NO);
        spu.setTitle("Mock商品" + id);
        spu.setTitleTrans("Mock Product " + id);
        return spu;
    }

    private TzProductSku mockSku(Long id, Long spuId, BigDecimal price) {
        TzProductSku sku = new TzProductSku();
        sku.setId(id);
        sku.setSpuId(spuId);
        sku.setPrice(price);
        sku.setPlatformProductId(String.valueOf(spuId));
        sku.setPlatformSku(String.valueOf(id));
        sku.setPlatformSpecId(String.valueOf(id));
        // sku.setSpecs("Mock规格" + id); // 跳过规格设置，测试不需要
        sku.setImage("https://mock.image/" + id + ".jpg");
        return sku;
    }

    private OrderPreviewVO mockPreviewVO(BigDecimal totalFreight) {
        OrderPreviewVO preview = new OrderPreviewVO();
        OrderPreviewVO.PriceDetails priceDetails = new OrderPreviewVO.PriceDetails();
        priceDetails.setShippingAmount(totalFreight);
        priceDetails.setMerchandiseAmount(new BigDecimal("540.00"));
        preview.setPriceDetails(priceDetails);
        return preview;
    }

    /**
     * 测试专用的buildContext方法，避免依赖外部ID生成器
     */
    private OrderContextDTO buildContextForTest(MockTestData mockData) {
        // 使用OrderContextHelper的分组逻辑
        Map<String, List<OrderItemInfo>> supplierGroupMap = OrderContextHelper.groupBySupplier(
            mockData.productSkuList, mockData.spuMap, mockData.skuQuantityMap);

        // 创建测试专用的采购单、供应商订单和订单项
        TzOrderPurchase purchaseOrder = createTestPurchaseOrder(mockData, supplierGroupMap);
        List<TzOrderSupplier> supplierOrders = createTestSupplierOrders(purchaseOrder, supplierGroupMap, mockData);
        List<TzOrderItem> orderItems = createTestOrderItems(purchaseOrder, supplierOrders, supplierGroupMap, mockData);

        return OrderContextDTO.builder()
            .purchaseOrder(purchaseOrder)
            .supplierOrders(supplierOrders)
            .orderItems(orderItems)
            .shoppingCartIds(mockData.shoppingCartIds)
            .build();
    }

    /**
     * 创建测试专用的采购订单
     */
    private TzOrderPurchase createTestPurchaseOrder(MockTestData mockData,
        Map<String, List<OrderItemInfo>> supplierGroupMap) {
        // 计算订单汇总信息
        int totalQuantity = supplierGroupMap.values().stream()
            .flatMap(List::stream)
            .mapToInt(OrderItemInfo::getQuantity)
            .sum();
        int lineItemCount = supplierGroupMap.values().stream()
            .mapToInt(List::size)
            .sum();

        // 计算商品总金额
        BigDecimal goodsAmount = supplierGroupMap.values().stream()
            .flatMap(List::stream)
            .map(item -> item.getSku().getPrice().multiply(BigDecimal.valueOf(item.getQuantity())))
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算服务费
        BigDecimal serviceFee = goodsAmount.multiply(mockData.serviceFeeRate).setScale(2, RoundingMode.HALF_UP);

        // 计算订单总金额
        BigDecimal totalAmount = goodsAmount.add(mockData.totalFreight).add(serviceFee);

        return TzOrderPurchase.builder()
            .id(generateTestId())
          .purchaseOrderNo("N" + generateTestId())
            .buyerId(mockData.userId)
            .customerGoodsAmount(goodsAmount)
            .customerTotalFreight(mockData.totalFreight)
            .serviceFee(serviceFee)
            .customerTotalAmount(totalAmount)
            .supplierCount(supplierGroupMap.size())
            .lineItemCount(lineItemCount)
            .totalQuantity(totalQuantity)
            .exchangeRateSnapshot(new BigDecimal("0.13"))
            .build();
    }

    /**
     * 创建测试专用的供应商订单
     */
    private List<TzOrderSupplier> createTestSupplierOrders(TzOrderPurchase purchaseOrder,
        Map<String, List<OrderItemInfo>> supplierGroupMap, MockTestData mockData) {
        List<TzOrderSupplier> supplierOrders = new ArrayList<>();

        for (Map.Entry<String, List<OrderItemInfo>> entry : supplierGroupMap.entrySet()) {
            String supplierId = entry.getKey();
            List<OrderItemInfo> items = entry.getValue();

            // 计算该供应商的商品金额
            BigDecimal supplierGoodsAmount = items.stream()
                .map(item -> item.getSku().getPrice().multiply(BigDecimal.valueOf(item.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            // ❌ 当前代码的错误逻辑：运费固定为0
            BigDecimal supplierFreightAmount = BigDecimal.ZERO;

            // 计算该供应商的服务费
            BigDecimal supplierServiceFee = supplierGoodsAmount.multiply(mockData.serviceFeeRate)
                .setScale(2, RoundingMode.HALF_UP);

            // 计算应付金额
            BigDecimal userPayableAmount = supplierGoodsAmount.add(supplierFreightAmount).add(supplierServiceFee);

            TzOrderSupplier supplierOrder = TzOrderSupplier.builder()
                .id(generateTestId())
                .purchaseOrderId(purchaseOrder.getId())
                .supplierOrderNo("SO" + generateTestId())
                .supplierId(supplierId)
                .supplierName(items.get(0).getSpu().getSourcePlatformSellerName())
                .customerGoodsAmount(supplierGoodsAmount)
                .customerFreightAmount(supplierFreightAmount) // ❌ 这里是问题所在
                .customerTotalAmount(userPayableAmount)
                .serviceFee(supplierServiceFee)
                .lineItemCount(items.size())
                .build();

            supplierOrders.add(supplierOrder);
        }
        return supplierOrders;
    }

    /**
     * 创建测试专用的订单项
     */
    private List<TzOrderItem> createTestOrderItems(TzOrderPurchase purchaseOrder, List<TzOrderSupplier> supplierOrders,
        Map<String, List<OrderItemInfo>> supplierGroupMap, MockTestData mockData) {
        List<TzOrderItem> orderItems = new ArrayList<>();

        for (TzOrderSupplier supplierOrder : supplierOrders) {
            String supplierId = supplierOrder.getSupplierId();
            List<OrderItemInfo> items = supplierGroupMap.get(supplierId);

            if (items != null) {
                int lineNumber = 1;
                for (OrderItemInfo itemInfo : items) {
                    BigDecimal unitPrice = itemInfo.getSku().getPrice();
                    int orderedQuantity = itemInfo.getQuantity();
                    BigDecimal lineTotalAmount = unitPrice.multiply(BigDecimal.valueOf(orderedQuantity));

                    TzOrderItem orderItem = TzOrderItem.builder()
                        .id(generateTestId())
                        .purchaseOrderId(purchaseOrder.getId())
                        .supplierOrderId(supplierOrder.getId())
                        .lineNumber(lineNumber++)
                        .productSpuId(itemInfo.getSpu().getId())
                        .productSkuId(itemInfo.getSku().getId())
                        .price(unitPrice)
                        .quantity(BigDecimal.valueOf(orderedQuantity))
                        .totalAmount(lineTotalAmount)
                        .build();
                    orderItems.add(orderItem);
                }
            }
        }
        return orderItems;
    }

    /**
     * 生成测试专用的ID（使用时间戳+随机数）
     */
    private Long generateTestId() {
        return System.currentTimeMillis() + (long) (Math.random() * 1000);
    }

    // ========== 测试数据类 ==========

    static class MockTestData {

        Long userId;
        BigDecimal serviceFeeRate;
        BigDecimal totalFreight;
        Map<Long, Integer> skuQuantityMap;
        List<TzProductSku> productSkuList;
        Map<Long, TzProductSpu> spuMap;
        List<Long> shoppingCartIds;
        OrderPreviewVO previewVO;
        CreateOrderSubmitReq orderSubmitReq;
    }
}
