/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.handler;

import com.fulfillmen.shop.manager.core.repository.SysAlibabaCallbackLogsRepository;
import com.fulfillmen.support.alibaba.enums.CallbackMessageType;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler;
import com.fulfillmen.support.alibaba.webhook.MessageEvent;
import com.fulfillmen.support.alibaba.webhook.data.OrderMessage;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 订单事件处理器 - 优化版本
 *
 * <pre>
 * 职责：
 * 1. 接收和验证webhook消息
 * 2. 路由到相应的业务处理服务
 * 3. 统一的异常处理和日志记录
 * 4. 支持新旧版本数据的兼容处理
 *
 * 优化要点：
 * - 简化Handler职责，将业务逻辑委托给Service层
 * - 改进异常处理和日志记录
 * - 集成订单状态管理系统
 * - 支持数据补齐和兼容性处理
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/11 10:52
 * @description 订单webhook事件处理器，支持新旧版本数据兼容
 * @since 1.0.0
 */
@Slf4j
@Component
public class OrderHandler extends AbstractTypedMessageHandler<OrderMessage> {

    /**
     * 支持的消息类型
     */
    private static final CallbackMessageType[] ORDER_SUPPORTED_TYPES = List.of(
      /*
       * 订单创建
       */
      OrderMessageTypeEnums.ORDER_BUYER_VIEW_BUYER_MAKE,
      /*
       * 订单付款
       */
      OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_PAY,
      /*
       * 批量订单付款
       */
      OrderMessageTypeEnums.ORDER_BATCH_PAY,
      /*
       * 订单发货
       */
      OrderMessageTypeEnums.ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS,
      /*
       * 订单部分发货
       */
      OrderMessageTypeEnums.ORDER_BUYER_VIEW_PART_PART_SENDGOODS,
      /*
       * 订单确认收货
       */
      OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS,
      /*
       * 订单交易成功
       */
      OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_SUCCESS,
      /*
       * 订单改价
       */
      OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY).toArray(new CallbackMessageType[0]);
    private final SysAlibabaCallbackLogsRepository callbackLogsRepository;

    public OrderHandler(SysAlibabaCallbackLogsRepository callbackLogsRepository) {
        // 构造器中指定支持的消息类型
        super(ORDER_SUPPORTED_TYPES);
        this.callbackLogsRepository = callbackLogsRepository;
    }

    @Override
    public List<CallbackMessageType> getSupportedTypes() {
        log.info("支持的消息类型: {}", super.getSupportedTypes());
        return super.getSupportedTypes();
    }

    @Override
    public boolean canHandle(CallbackMessageType messageType) {
        return super.canHandle(messageType);
    }

    @Override
    protected void doHandle(OrderMessage data, MessageEvent<OrderMessage> event) {
        String messageType = event.getType().getMessageType();
        String orderId = String.valueOf(data.getOrderId());
        String msgId = event.getMsgId();
        Long logId = null;
        try {
            log.info("接收到订单webhook消息: msgId={}, type={}, orderId={}, status={}, gmtBorn={}",
              msgId, messageType, orderId, data.getCurrentStatus(), event.getGmtBorn());

            // 验证消息类型
            OrderMessageTypeEnums orderMessageTypeEnums = OrderMessageTypeEnums.fromMessageType(messageType);
            if (orderMessageTypeEnums == null) {
                log.warn("不支持的订单消息类型: messageType={}, orderId={}", messageType, orderId);
                return;
            }

            // 仅记录日志，不处理业务逻辑 - 业务逻辑由延迟处理任务处理
            logId = callbackLogsRepository.createProcessingLog(
              event.getRawData(),
              null,
              messageType,
              data.getOrderId(),
              event.getReceivedAt());

            if (logId != null) {
                log.info("Webhook消息已记录，等待延迟处理: msgId={}, logId={}, orderId={}, gmtBorn={}", msgId, logId, orderId, event.getGmtBorn());
                // 记录成功后立即标记为成功（这里的成功指的是成功记录，不是业务处理成功）
                callbackLogsRepository.markSuccess(logId);
            } else {
                log.error("记录Webhook消息失败: msgId={}, orderId={}", msgId, orderId);
            }

        } catch (Exception e) {
            log.error("处理Webhook消息记录异常: msgId={}, type={}, orderId={}, error={}",
              msgId, messageType, orderId, e.getMessage(), e);
            // 记录失败不抛异常，避免影响webhook响应
        }
    }

    @Override
    protected Class<OrderMessage> getDataClass() {
        return OrderMessage.class;
    }
}
