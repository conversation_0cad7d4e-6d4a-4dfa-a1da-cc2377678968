/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.processor.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemLogisticsStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.manager.support.alibaba.webhook.convert.AlibabaOrderConvert;
import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.shop.manager.support.alibaba.webhook.processor.OrderEventProcessor;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeBaseInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeNativeLogisticsInfo.LogisticsItem;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse.OrderDetail;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.enums.OrderStatusEnums;
import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单发货事件处理器
 * <p>
 * 策略实现类，专门处理 {@link OrderMessageTypeEnums#ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS}
 * (全部发货)
 * 和 {@link OrderMessageTypeEnums#ORDER_BUYER_VIEW_PART_PART_SENDGOODS} (部分发货)
 * 事件。
 * </p>
 *
 * <AUTHOR>
 * @date 2025/08/08
 * @version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderShipmentProcessor implements OrderEventProcessor {

    private final TzOrderSupplierMapper tzOrderSupplierMapper;
    private final TzOrderPurchaseMapper tzOrderPurchaseMapper;
    private final TzOrderItemMapper tzOrderItemMapper;
    private final IWmsManager wmsManager;
    private final TransactionTemplate transactionTemplate;
    private final AlibabaOrderConvert alibabaOrderConvert;

    @Override
    public void process(OrderContextRecord context) {
        String orderId = context.getAlibabaOrderIdStr();
        log.info("开始处理订单发货事件: orderId={}", orderId);

        if (!isShipmentProcessable(context)) {
            log.warn("订单状态不满足发货处理条件，跳过处理。orderId={}, 1688_status={}", orderId, context.getAlibabaOrderStatus());
            return;
        }

        try {
            updateLocalOrderStatusToShipped(context);
            createWmsInboundOrder(context);

            // TODO: 发送发货通知等后续流程
            // sendShipmentNotification(orderId);

            log.info("订单发货事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("处理订单发货事件时发生异常: orderId={}, error={}", orderId, e.getMessage(), e);
            throw new RuntimeException("处理订单发货事件失败: orderId=" + orderId, e);
        }
    }

    /**
     * 前置条件检查：确认订单状态是否可以进行“发货”处理。
     */
    private boolean isShipmentProcessable(OrderContextRecord context) {
        OrderStatusEnums status = context.getAlibabaOrderStatus();
        // 收到发货消息时，订单状态可能是“待收货”或某些场景下还是“待发货”（消息延迟）
        return status == OrderStatusEnums.WAIT_BUYER_RECEIVE || status == OrderStatusEnums.WAIT_SELLER_SEND;
    }

    /**
     * 更新本地数据库中的订单状态为“已发货”。
     */
    private void updateLocalOrderStatusToShipped(OrderContextRecord context) {
        String orderId = context.getAlibabaOrderIdStr();
        log.debug("更新本地订单状态为已发货: orderId={}", orderId);

        OrderDetail orderDetail = context.alibabaOrderDetail();
        TzOrderPurchase purchase = context.tzOrderPurchase();
        TzOrderSupplier orderSupplier = context.getTzOrderSupplierByOrderId(orderId);
        TradeBaseInfo baseInfo = orderDetail.getBaseInfo();

        List<String> trackingNos = orderDetail.getNativeLogistics().getLogisticsItems().stream()
            .map(LogisticsItem::getLogisticsBillNo)
            .toList();

        orderSupplier.setStatus(
            alibabaOrderConvert.convertSupplierOrderStatus(OrderStatusEnums.fromCode(baseInfo.getStatus())));
        orderSupplier.setShippedDate(LocalDateTime.now());
        orderSupplier.setPlatformTrackingNo(String.join(",", trackingNos));

        purchase.setOrderStatus(TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED);

        context.tzOrderItems().forEach(item -> item.setLogisticsStatus(TzOrderItemLogisticsStatusEnum.SHIPPED));

        transactionTemplate.executeWithoutResult(transactionStatus -> {
            try {
                tzOrderSupplierMapper.updateById(orderSupplier);
                tzOrderPurchaseMapper.updateById(purchase);
                tzOrderItemMapper.updateBatchById(context.tzOrderItems());
            } catch (Exception e) {
                log.error("更新发货状态到数据库时失败: purchaseNo={}, error={}", context.getPurchaseOrderNo(), e.getMessage(), e);
                transactionStatus.setRollbackOnly();
                throw e;
            }
        });
    }

    /**
     * 在WMS中创建入库单。
     */
    private void createWmsInboundOrder(OrderContextRecord context) {
        log.debug("在WMS中创建入库单: orderId={}", context.getAlibabaOrderIdStr());

        WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetail = context.getWmsPurchaseOrderDetail();
        OrderDetail orderDetail = context.alibabaOrderDetail();
        TradeBaseInfo baseInfo = orderDetail.getBaseInfo();
        List<String> trackingNos = orderDetail.getNativeLogistics().getLogisticsItems().stream()
            .map(LogisticsItem::getLogisticsBillNo).toList();

        wmsPurchaseOrderDetail.setTrackingNo(trackingNos.isEmpty() ? null : trackingNos.getFirst());
        wmsPurchaseOrderDetail.setStatus(WmsOrderStatusEnum.SHIPPED_PENDING_RECEIPT);
        wmsPurchaseOrderDetail.setShippingTime(Objects.nonNull(baseInfo.getAllDeliveredTime()) ? baseInfo.getAllDeliveredTime() : LocalDateTime.now());

        this.wmsManager.inboundCreateByPurchase(wmsPurchaseOrderDetail);
    }

    @Override
    public List<OrderMessageTypeEnums> supports() {
        return List.of(
            OrderMessageTypeEnums.ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS,
            OrderMessageTypeEnums.ORDER_BUYER_VIEW_PART_PART_SENDGOODS);
    }
}
