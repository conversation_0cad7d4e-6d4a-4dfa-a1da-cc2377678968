/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.notification.service.impl;

import com.fulfillmen.shop.manager.support.notification.service.WeChatNotificationService;
import com.fulfillmen.starter.core.util.JacksonUtil;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

/**
 * 企业微信消息通知服务实现
 *
 * <AUTHOR>
 * @date 2025/1/3
 * @description 企业微信群机器人消息推送服务实现
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WeChatNotificationServiceImpl implements WeChatNotificationService {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final WebClient fmWebClient;
    @Value("${fulfillmen.notification.wechat.webhook-url:}")
    private String webhookUrl;
    @Value("${fulfillmen.notification.wechat.enabled:true}")
    private boolean enabled;
    @Value("${fulfillmen.notification.wechat.timeout:5000}")
    private int timeoutMs;

    @Async
    @Override
    public void sendCallbackRetryFailedNotification(Long orderId, String eventType, int retryCount,
      String failedMessage, String lastFailedTime) {
        if (!enabled || webhookUrl.isEmpty()) {
            log.debug("企业微信通知未启用或未配置webhook地址");
            return;
        }

        try {
            String content = buildCallbackRetryFailedMessage(orderId, eventType, retryCount, failedMessage,
              lastFailedTime);
            sendMarkdownMessage("🚨 阿里巴巴回调重试失败通知", content);
            log.info("发送阿里巴巴回调重试失败通知成功: orderId={}, retryCount={}", orderId, retryCount);
        } catch (Exception e) {
            log.error("发送阿里巴巴回调重试失败通知异常: orderId={}", orderId, e);
        }
    }

    @Async
    @Override
    public void sendManualSyncNotification(String operatorName, String orderId, boolean syncResult, String message) {
        if (!enabled || webhookUrl.isEmpty()) {
            log.debug("企业微信通知未启用或未配置webhook地址");
            return;
        }

        try {
            String emoji = syncResult ? "✅" : "❌";
            String status = syncResult ? "成功" : "失败";
            String content = buildManualSyncMessage(operatorName, orderId, status, message);
            sendMarkdownMessage(emoji + " 手动同步订单通知", content);
            log.info("发送手动同步订单通知成功: orderId={}, result={}", orderId, syncResult);
        } catch (Exception e) {
            log.error("发送手动同步订单通知异常: orderId={}", orderId, e);
        }
    }

    @Async
    @Override
    public void sendSystemErrorNotification(String title, String content) {
        if (!enabled || webhookUrl.isEmpty()) {
            log.debug("企业微信通知未启用或未配置webhook地址");
            return;
        }

        try {
            sendMarkdownMessage("⚠️ " + title, content);
            log.info("发送系统异常通知成功: title={}", title);
        } catch (Exception e) {
            log.error("发送系统异常通知异常: title={}", title, e);
        }
    }

    @Override
    public boolean testConnection() {
        if (!enabled || webhookUrl.isEmpty()) {
            log.warn("企业微信通知未启用或未配置webhook地址");
            return false;
        }

        try {
            String testContent = String.format("📡 **连接测试**\n\n" +
                "> 测试时间: %s\n" +
                "> 系统: Fulfillmen Shop\n" +
                "> 状态: 连接正常",
              LocalDateTime.now().format(FORMATTER));

            sendMarkdownMessage("🔔 企业微信通知测试", testContent);
            log.info("企业微信连接测试成功");
            return true;
        } catch (Exception e) {
            log.error("企业微信连接测试失败", e);
            return false;
        }
    }

    /**
     * 发送 Markdown 格式消息
     */
    private void sendMarkdownMessage(String title, String content) {
        Map<String, Object> message = Map.of(
          "msgtype", "markdown",
          "markdown", Map.of(
            "content", "## " + title + "\n\n" + content));

        try {
            ResponseEntity<String> response = fmWebClient.post()
              .uri(webhookUrl)
              .contentType(MediaType.APPLICATION_JSON)
              .bodyValue(JacksonUtil.toJsonString(message))
              .retrieve()
              .toEntity(String.class)
              .timeout(Duration.ofMillis(timeoutMs))
              .block();

            if (response != null && response.getStatusCode().is2xxSuccessful()) {
                log.debug("企业微信消息发送成功: title={}", title);
            } else {
                log.warn("企业微信消息发送失败: title={}, status={}, response={}",
                  title, response != null ? response.getStatusCode() : "null",
                  response != null ? response.getBody() : "null");
            }
        } catch (WebClientResponseException e) {
            log.error("企业微信消息发送HTTP异常: title={}, status={}, response={}",
              title, e.getStatusCode(), e.getResponseBodyAsString(), e);
            throw e;
        } catch (Exception e) {
            log.error("企业微信消息发送异常: title={}", title, e);
            throw e;
        }
    }

    /**
     * 构建回调重试失败消息内容
     */
    private String buildCallbackRetryFailedMessage(Long orderId, String eventType, int retryCount,
      String failedMessage, String lastFailedTime) {
        return String.format(
          "📋 **订单信息**\n" +
            "> 订单ID: `%s`\n" +
            "> 事件类型: `%s`\n" +
            "> 重试次数: `%d`\n\n" +
            "⏰ **时间信息**\n" +
            "> 最后失败时间: `%s`\n" +
            "> 通知时间: `%s`\n\n" +
            "❌ **失败原因**\n" +
            "> %s\n\n" +
            "🔧 **处理建议**\n" +
            "> 1. 检查订单数据完整性\n" +
            "> 2. 确认第三方接口可用性\n" +
            "> 3. 考虑手动同步处理\n" +
            "> 4. 联系技术人员排查",
          orderId, eventType, retryCount, lastFailedTime,
          LocalDateTime.now().format(FORMATTER),
          truncateMessage(failedMessage, 200));
    }

    /**
     * 构建手动同步消息内容
     */
    private String buildManualSyncMessage(String operatorName, String orderId, String status, String message) {
        return String.format(
          "👤 **操作信息**\n" +
            "> 操作员: `%s`\n" +
            "> 操作时间: `%s`\n\n" +
            "📋 **订单信息**\n" +
            "> 订单ID: `%s`\n" +
            "> 同步状态: `%s`\n\n" +
            "📝 **处理详情**\n" +
            "> %s",
          operatorName, LocalDateTime.now().format(FORMATTER),
          orderId, status, truncateMessage(message, 300));
    }

    /**
     * 截断消息内容
     */
    private String truncateMessage(String message, int maxLength) {
        if (message == null || message.length() <= maxLength) {
            return message != null ? message : "无详细信息";
        }
        return message.substring(0, maxLength) + "...";
    }
}
