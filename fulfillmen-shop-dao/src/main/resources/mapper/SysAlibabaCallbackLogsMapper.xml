<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fulfillmen.shop.dao.mapper.SysAlibabaCallbackLogsMapper">
  <resultMap id="BaseResultMap" type="com.fulfillmen.shop.domain.entity.SysAlibabaCallbackLogs">
    <!--@mbg.generated-->
    <!--@Table sys_alibaba_callback_logs-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="metadata" jdbcType="VARCHAR" property="metadata" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="event_type" jdbcType="VARCHAR" property="eventType" />
    <result column="received_timestamp" jdbcType="TIMESTAMP" property="receivedTimestamp" />
    <result column="process_status" jdbcType="TINYINT" property="processStatus" />
    <result column="process_failed_msg" jdbcType="LONGVARCHAR" property="processFailedMsg" />
    <result column="retry_count" jdbcType="TINYINT" property="retryCount" />
    <result column="sign" jdbcType="VARCHAR" property="sign" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_born_virtual" jdbcType="BIGINT" property="gmtBornVirtual" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, metadata, order_id, event_type, received_timestamp, process_status, process_failed_msg,
    retry_count,sign, gmt_created, gmt_modified,gmt_born_virtual
  </sql>

  <select id="findLatestUnprocessedLogs" resultMap="BaseResultMap">

  </select>
</mapper>
