/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.processor.impl;

import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemLogisticsStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.shop.manager.support.alibaba.webhook.processor.OrderEventProcessor;
import com.fulfillmen.starter.core.exception.BusinessException;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeProductItem;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.enums.OrderStatusEnums;
import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 订单确认收货事件处理器
 * <p>
 * 策略实现类，专门处理
 * {@link OrderMessageTypeEnums#ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS} 事件。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/08/08
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderConfirmationProcessor implements OrderEventProcessor {

    @Override
    public void process(OrderContextRecord context) {
        String orderId = context.getAlibabaOrderIdStr();
        log.info("开始处理订单确认收货事件: orderId={}", orderId);

        try {
            // 1. 更新本地订单状态为"已确认收货"
            updateLocalOrderStatusToConfirmed(context);
            // 2. 通知WMS系统更新入库状态
            // 3. 触发质检流程
            // 4. 更新库存信息

            log.warn("订单确认收货逻辑尚未完全实现: orderId={}", orderId);

            log.info("订单确认收货事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("处理订单确认收货事件时发生异常: orderId={}, error={}", orderId, e.getMessage(), e);
            throw new BusinessException("处理订单确认收货事件失败: orderId=" + orderId, e);
        }
    }

    private void updateLocalOrderStatusToConfirmed(OrderContextRecord context) {
        List<TradeProductItem> alibabaOrderProducts = context.getAlibabaOrderProducts();
        WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetail = context.getWmsPurchaseOrderDetail();
        WmsOrderStatusEnum status = wmsPurchaseOrderDetail.getStatus();
        TzOrderPurchase tzOrderPurchase = context.tzOrderPurchase();
        // 获取供应商订单
        TzOrderSupplier orderSuppliers = context.getTzOrderSupplierByOrderId(context.getAlibabaOrderIdStr());
        List<TzOrderItem> orderItems = context.getOrderItemsBySupplierOrderId(orderSuppliers.getId());

        // 获取 alibaba 商品的收货信息，查看那些商品已经确认收货。 如果所有商品都已收货，则更新采购订单状态为已完成
        List<TradeProductItem> productItems = alibabaOrderProducts.stream().filter(productItem -> {
            Integer logisticsStatus = productItem.getLogisticsStatus();
            TzOrderItemLogisticsStatusEnum itemLogisticsStatusEnum = TzOrderItemLogisticsStatusEnum
              .getByCode(logisticsStatus);
            return Objects.equals(itemLogisticsStatusEnum, TzOrderItemLogisticsStatusEnum.RECEIVED);
        }).toList();

        // 如果所有商品都已收货，则更新采购订单状态为已完成
        OrderStatusEnums alibabaOrderStatus = context.getAlibabaOrderStatus();
        // 已确认收货 或 已交易完成
        if (Objects.equals(OrderStatusEnums.CONFIRM_GOODS_AND_HAS_SUBSIDY, alibabaOrderStatus)
          || Objects.equals(OrderStatusEnums.SUCCESS, alibabaOrderStatus)) {
            // 订单完成
            wmsPurchaseOrderDetail.setStatus(WmsOrderStatusEnum.COMPLETED);
            orderSuppliers.setStatus(TzOrderSupplierStatusEnum.COMPLETED);
            // 同时商品信息也变更
            orderItems.forEach(item -> {
                item.setLogisticsStatus(TzOrderItemLogisticsStatusEnum.RECEIVED);
                item.setStatus(TzOrderItemStatusEnum.COMPLETED);
            });
        } else {
            // 客户确认签收
            wmsPurchaseOrderDetail.setStatus(WmsOrderStatusEnum.COMPLETED);
            orderSuppliers.setStatus(TzOrderSupplierStatusEnum.COMPLETED);
            // 同时商品信息也变更
            orderItems.forEach(item -> {
                item.setLogisticsStatus(TzOrderItemLogisticsStatusEnum.RECEIVED);
                item.setStatus(TzOrderItemStatusEnum.COMPLETED);
            });
        }

    }

    @Override
    public List<OrderMessageTypeEnums> supports() {
        return List.of(OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS);
    }
}
