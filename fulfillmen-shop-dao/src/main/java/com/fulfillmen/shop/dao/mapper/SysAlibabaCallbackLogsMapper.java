/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.dao.mapper;

import com.fulfillmen.shop.domain.entity.SysAlibabaCallbackLogs;
import com.fulfillmen.starter.data.mp.base.BaseMapper;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2025/8/8 09:30
 * @description: todo
 * @since 1.0.0
 */
@Mapper
public interface SysAlibabaCallbackLogsMapper extends BaseMapper<SysAlibabaCallbackLogs> {

    /**
     * 查找延迟处理的未处理记录 - 按订单ID分组，每组取最新的业务时间戳记录
     *
     * @param limit        限制数量
     * @return 每个订单ID的最新未处理记录列表
     */
    List<SysAlibabaCallbackLogs> findLatestUnprocessedLogs(int limit);
}
