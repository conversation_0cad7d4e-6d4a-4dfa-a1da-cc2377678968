/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.service;

import com.fulfillmen.support.alibaba.enums.LogisticsMessageTypeEnums;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.webhook.MessageEvent;
import com.fulfillmen.support.alibaba.webhook.data.LogisticsMessage;
import com.fulfillmen.support.alibaba.webhook.data.OrderMessage;

/**
 * 订单Webhook业务处理服务接口 - 策略模式重构版
 *
 * <pre>
 * 重构后职责：
 * 1. 处理订单webhook消息的编排和路由
 * 2. 协调数据补齐和兼容性处理
 * 3. 集成订单状态管理系统
 * 4. 提供统一的错误处理和恢复机制
 * 5. 通过策略模式将具体的业务逻辑委托给专门的处理器
 *
 * 注意：原有的 handle... 方法已迁移到各自的策略处理器中
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/8/8 (策略模式重构版)
 * @description 订单webhook业务处理服务，使用策略模式重构
 * @since 1.0.0
 */
public interface OrderWebhookService {

    /**
     * 处理订单webhook消息
     *
     * <pre>
     * 处理流程：
     * 1. 验证消息有效性
     * 2. 检查数据存在性（新版/旧版判断）
     * 3. 执行数据补齐（如果需要）
     * 4. 通过策略注册表路由到具体的业务处理器
     * 5. 触发相关事件和状态更新
     * </pre>
     *
     * @param orderMessage 订单消息数据
     * @param messageEvent 消息事件
     * @param messageType  消息类型枚举
     */
    void processOrderWebhook(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
        OrderMessageTypeEnums messageType);

    /**
     * 处理物流webhook消息
     *
     * <pre>
     * 处理流程：
     * 1. 验证消息有效性
     * 2. 检查数据存在性（新版/旧版判断）
     * 3. 执行数据补齐（如果需要）
     * 4. 通过策略注册表路由到具体的业务处理器
     * 5. 触发相关事件和状态更新
     * </pre>
     *
     * @param logisticsMessage 物流消息数据
     * @param messageEvent     消息事件
     * @param messageType      消息类型枚举
     */
    void processLogisticsWebhook(LogisticsMessage logisticsMessage, MessageEvent<LogisticsMessage> messageEvent,
      LogisticsMessageTypeEnums messageType);
}
