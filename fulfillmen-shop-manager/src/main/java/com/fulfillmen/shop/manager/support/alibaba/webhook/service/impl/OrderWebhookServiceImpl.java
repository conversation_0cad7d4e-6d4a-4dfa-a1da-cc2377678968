/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl;

import com.fulfillmen.shop.manager.support.alibaba.IOrderManager;
import com.fulfillmen.shop.manager.support.alibaba.webhook.event.OrderWebhookEvent;
import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.shop.manager.support.alibaba.webhook.processor.OrderEventProcessor;
import com.fulfillmen.shop.manager.support.alibaba.webhook.processor.OrderEventProcessorRegistry;
import com.fulfillmen.shop.manager.support.alibaba.webhook.service.OrderDataSyncService;
import com.fulfillmen.shop.manager.support.alibaba.webhook.service.OrderWebhookService;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.fulfillmen.starter.core.exception.BusinessException;
import com.fulfillmen.support.alibaba.api.request.order.OrderDetailRequestRecord;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse;
import com.fulfillmen.support.alibaba.enums.LogisticsMessageTypeEnums;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.enums.OrderStatusEnums;
import com.fulfillmen.support.alibaba.webhook.MessageEvent;
import com.fulfillmen.support.alibaba.webhook.data.LogisticsMessage;
import com.fulfillmen.support.alibaba.webhook.data.OrderMessage;
import com.fulfillmen.support.wms.dto.request.PurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 订单Webhook业务处理服务实现 - 策略模式重构版
 *
 * <pre>
 * 重构要点：
 * 1. 使用策略模式，将具体的业务处理逻辑下沉到独立的处理器中
 * 2. 本类专注于数据获取、同步和路由，不再包含具体的业务逻辑
 * 3. 通过 OrderEventProcessorRegistry 动态分发消息到对应的处理器
 * 4. 保持统一的异常处理和日志记录
 * 5. 简化了类的职责，提高了可维护性和可扩展性
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/8/8 (重构版本)
 * @description 订单webhook业务处理服务实现，使用策略模式
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderWebhookServiceImpl implements OrderWebhookService {

    private final OrderDataSyncService orderDataSyncService;
    private final IOrderManager orderManager;
    private final IWmsManager wmsManager;
    private final ApplicationEventPublisher eventPublisher;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final OrderEventProcessorRegistry processorRegistry;

    @Override
    public void processOrderWebhook(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
      OrderMessageTypeEnums messageType) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        String msgId = messageEvent.getMsgId();

        try {
            log.info("开始处理订单webhook消息: orderId={}, msgId={}, messageType={}, currentStatus={}",
              orderId, msgId, messageType.getMessageType(), orderMessage.getCurrentStatus());

            // 校验订单状态是否符合
            OrderStatusEnums orderStatusEnums = OrderStatusEnums.fromCode(orderMessage.getCurrentStatus());
            if (orderStatusEnums == null) {
                log.warn("暂不支持的处理流程里: [{}]", orderMessage.getCurrentStatus());
                return;
            }

            // 1. 异步获取订单数据
            CompletableFuture<OrderDetailResponse.OrderDetail> orderDetailFuture = getOrderDetailAsync(
              orderMessage.getOrderId());
            CompletableFuture<List<WmsPurchaseOrderDetailsRes>> wmsOrderDetailsFuture = getWmsOrderDetailsAsync(
              orderId);

            // 等待全部任务完成
            CompletableFuture.allOf(orderDetailFuture, wmsOrderDetailsFuture).join();

            // 2. 等待数据获取完成
            OrderDetailResponse.OrderDetail orderDetail = orderDetailFuture.join();
            List<WmsPurchaseOrderDetailsRes> wmsOrderDetails = wmsOrderDetailsFuture.join();

            log.info("订单数据获取完成: orderId={}, alibabaOrderDetail={}, wmsOrderDetails={}",
              orderId, orderDetail != null, wmsOrderDetails != null ? wmsOrderDetails.size() : 0);

            // 3. 验证数据有效性
            validateOrderData(orderDetail, wmsOrderDetails, orderId);

            // 4. 检查数据完整性
            OrderDataSyncService.OrderDataIntegrityResult integrityResult = orderDataSyncService.checkOrderDataIntegrity(orderDetail);

            // 5. 如果 wms 为 null，并且供应商订单存在，则通过供应商订单获取 wms 采购订单号
            // 这步骤属于补救措施，再次确认数据是否正确
            if (Objects.isNull(wmsOrderDetails) && integrityResult.hasSupplierOrders()) {
                var currentOrderSupplier = integrityResult.existingSupplierOrders().stream()
                  .filter(orderSupplier -> Objects.equals(orderSupplier.getPlatformOrderId(), orderId))
                  .findFirst().orElse(null);
                // 通过供应商订单获取 wms 采购订单号
                wmsOrderDetails = getWmsOrderDetailsBySupplierOrderAsync(currentOrderSupplier).join();
                if (!CollectionUtils.isEmpty(wmsOrderDetails)) {
                    log.info("通过供应商订单获取 wms 采购订单成功: [{}]", wmsOrderDetails.size());
                    WmsPurchaseOrderDetailsRes wmsOrderDetailsFirst = wmsOrderDetails.getFirst();
                    if (Objects.isNull(wmsOrderDetailsFirst.getOrderId()) || wmsOrderDetailsFirst.getOrderId() <= 0L) {
                        wmsOrderDetails.getFirst().setOrderId(Long.valueOf(orderId));
                    }
                } else {
                    log.warn("请检查一下 wms 采购订单是否存在。");
                    throw new BusinessException("wms 采购订单不存在, 无法进行后续操作。");
                }
            }

            log.info("订单数据完整性检查结果: orderId={}, isComplete={}, isNewVersion={}, missing={}", orderId, integrityResult.isDataComplete(), integrityResult.isNewVersionData(),
              integrityResult.getMissingDataDescription());

            // 6. 同步和补齐数据
            OrderContextRecord orderContextRecord = orderDataSyncService.syncAndCompleteOrderData(orderId, integrityResult, orderDetail, wmsOrderDetails);

            // 7. 发布数据同步完成事件
            publishDataSyncCompletedEvent(orderMessage, messageEvent, messageType, orderContextRecord);

            // 8. 路由到具体的业务处理逻辑
            routeToBusinessLogic(orderMessage, messageEvent, messageType, orderContextRecord);

            log.info("订单webhook消息处理完成: orderId={}, msgId={}, messageType={}", orderId, msgId, messageType.getMessageType());

        } catch (Exception e) {
            log.error("订单webhook消息处理失败: orderId={}, msgId={}, messageType={}, error={}",
              orderId, msgId, messageType.getMessageType(), e.getMessage(), e);

            // 发布处理失败事件
            publishProcessingFailedEvent(orderMessage, messageEvent, messageType, e);
            throw e;
        }
    }


    @Override
    public void processLogisticsWebhook(LogisticsMessage logisticsMessage, MessageEvent<LogisticsMessage> messageEvent, LogisticsMessageTypeEnums messageType) {
        try {
            log.info("开始处理订单物流 webhook 消息: orderId={}, msgId={}, messageType={}, currentStatus={}");
            // TODO: 2025/8/13 待处理物流信息的更新和流转
        } catch (Exception e) {
            log.error("订单物流 webhook 消息处理失败: orderId={}, msgId={}, messageType={}, error={}",
              messageType.getMessageType(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据当前的 webhook order 订单 ID，获取到供应商订单，就能获取到 wms 订单
     *
     * <pre>
     * 补救措施
     * </pre>
     *
     * @param orderSupplier 供应商订单
     * @return List<WmsPurchaseOrderDetailsRes>
     */
    private CompletableFuture<List<WmsPurchaseOrderDetailsRes>> getWmsOrderDetailsBySupplierOrderAsync(
      Object orderSupplier) {
        return CompletableFuture.supplyAsync(() -> {
            if (Objects.isNull(orderSupplier)) {
                log.warn("供应商订单不存在");
                return null;
            }
            return null;
        }, threadPoolTaskExecutor);
    }

    /**
     * 异步获取1688订单详情
     */
    private CompletableFuture<OrderDetailResponse.OrderDetail> getOrderDetailAsync(Long orderId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                OrderDetailRequestRecord request = OrderDetailRequestRecord.builder()
                  .webSite("1688")
                  .orderId(orderId)
                  .build();
                return orderManager.getOrderDetail(request);
            } catch (Exception e) {
                log.error("获取1688订单详情失败: orderId={}", orderId, e);
                throw new RuntimeException("获取1688订单详情失败", e);
            }
        }, threadPoolTaskExecutor);
    }

    /**
     * 异步获取WMS订单详情
     */
    private CompletableFuture<List<WmsPurchaseOrderDetailsRes>> getWmsOrderDetailsAsync(String orderId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                PurchaseOrderDetailReq request = PurchaseOrderDetailReq.builder()
                  .orderId(orderId)
                  .build();
                return wmsManager.queryOrderDetail(request);
            } catch (Exception e) {
                log.error("获取WMS订单详情失败: orderId={}", orderId, e);
                return null;
            }
        }, threadPoolTaskExecutor);
    }

    /**
     * 验证订单数据有效性
     */
    private void validateOrderData(OrderDetailResponse.OrderDetail orderDetail,
      List<WmsPurchaseOrderDetailsRes> wmsOrderDetails,
      String orderId) {
        if (orderDetail == null) {
            throw new IllegalArgumentException("1688订单详情为空: orderId=" + orderId);
        }

        if (CollectionUtils.isEmpty(wmsOrderDetails)) {
            log.warn("WMS订单详情为空，可能是旧版数据: orderId={}", orderId);
            // 注意：这里不抛异常，因为旧版数据可能没有WMS记录，不存在没有采购记录。如果没有那么这个订单有问题。
            // throw new IllegalArgumentException("WMS订单详情为空: orderId=" + orderId);
        }
    }

    /**
     * 路由到具体的业务处理逻辑
     * <p>
     * 使用策略模式，根据消息类型动态分发到对应的处理器。 这种设计使得添加新的事件处理逻辑变得非常简单，无需修改此方法。
     * </p>
     */
    private void routeToBusinessLogic(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
      OrderMessageTypeEnums messageType, OrderContextRecord orderContextRecord) {
        log.info("路由到具体的业务处理逻辑: orderId={}, messageType={}", orderMessage.getOrderId(), messageType);

        // 对于退款消息，需要特殊的前置条件检查
        if (isRefundMessage(messageType)) {
            if (!isRefundActionSupported(orderMessage.getRefundAction())) {
                log.warn("不支持的退款操作类型，跳过处理。orderId={}, refundAction={}",
                  orderMessage.getOrderId(), orderMessage.getRefundAction());
                return;
            }
        }

        // 从注册表中获取对应的处理器
        Optional<OrderEventProcessor> processor = processorRegistry.getProcessor(messageType);
        if (processor.isEmpty()) {
            log.warn("未找到支持的订单消息处理器: messageType={}, orderId={}", messageType, orderMessage.getOrderId());
            return;
        }

        // 委托给具体的处理器执行业务逻辑
        try {
            processor.get().process(orderContextRecord);
            log.info("订单消息处理完成: orderId={}, messageType={}", orderMessage.getOrderId(), messageType);
        } catch (Exception e) {
            log.error("订单消息处理失败: orderId={}, messageType={}, error={}",
              orderMessage.getOrderId(), messageType, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 检查是否为退款消息类型
     */
    private boolean isRefundMessage(OrderMessageTypeEnums messageType) {
        return messageType == OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_BUYER_REFUND_IN_SALES ||
          messageType == OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_REFUND_AFTER_SALES;
    }

    /**
     * 检查退款操作类型是否被支持
     */
    private boolean isRefundActionSupported(String refundAction) {
        if (refundAction == null || refundAction.trim().isEmpty()) {
            return false;
        }
        // 使用与 OrderRefundProcessor 中相同的支持列表
        List<String> supportedActions = List.of(
          "SELLER_AGREE_REFUND",
          "SYSTEM_AGREE_REFUND_PROTOCOL",
          "SYSTEM_AGREE_REFUND",
          "SELLER_AGREE_REFUND_PROCOTOL");
        return supportedActions.contains(refundAction);
    }

    /**
     * 发布数据同步完成事件
     */
    private void publishDataSyncCompletedEvent(OrderMessage orderMessage,
      MessageEvent<OrderMessage> messageEvent,
      OrderMessageTypeEnums messageType,
      OrderContextRecord orderContextRecord) {
        OrderWebhookEvent event = OrderWebhookEvent.createDataSyncCompletedEvent(this, orderMessage, messageEvent,
          messageType, orderContextRecord);
        eventPublisher.publishEvent(event);
    }

    /**
     * 发布处理失败事件
     */
    private void publishProcessingFailedEvent(OrderMessage orderMessage,
      MessageEvent<OrderMessage> messageEvent,
      OrderMessageTypeEnums messageType,
      Exception error) {
        // TODO: 实现处理失败事件的发布
        log.error("发布订单处理失败事件: orderId={}, error={}", orderMessage.getOrderId(), error.getMessage());
    }
}
