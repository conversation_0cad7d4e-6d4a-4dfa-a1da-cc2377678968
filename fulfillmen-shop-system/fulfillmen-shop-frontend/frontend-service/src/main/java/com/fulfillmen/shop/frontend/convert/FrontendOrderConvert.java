/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.convert;

import cn.hutool.core.util.NumberUtil;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContext;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContextHolder;
import com.fulfillmen.shop.domain.dto.order.OrderItemInfo;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum;
import com.fulfillmen.shop.domain.util.CurrencyConversionUtils;
import com.fulfillmen.shop.domain.vo.OrderPreviewVO;
import com.fulfillmen.shop.domain.vo.OrderPreviewVO.ProductItemPreview;
import com.fulfillmen.shop.manager.support.alibaba.util.OrderErrorCodeUtil;
import com.fulfillmen.support.alibaba.api.response.model.CreateOrderPreviewResultCargoModel;
import com.fulfillmen.support.alibaba.api.response.model.OrderPreviewResult;
import com.fulfillmen.support.alibaba.api.response.order.OrderPreviewResponse;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 前端订单转换器
 *
 * <AUTHOR>
 * @date 2025/6/24
 * @description 前端订单数据转换器（重构版本，按产品和元单位分组）
 * @since 1.0.0
 */
@Slf4j
@Component
public class FrontendOrderConvert {

    /**
     * 服务费率（15%）
     */
    private static final BigDecimal SERVICE_FEE_RATE = new BigDecimal("0.15");

    /**
     * 构建订单预览响应为前端VO
     *
     * @param supplierResults 供应商预览结果列表
     * @return 订单预览VO
     */
    public OrderPreviewVO buildOrderPreviewVO(List<SupplierPreviewResult> supplierResults) {
        if (CollectionUtils.isEmpty(supplierResults)) {
            return buildEmptyOrderPreviewVO();
        }

        List<OrderPreviewVO.ProductItemPreview> productItems = new ArrayList<>();
        List<OrderPreviewVO.OrderPreviewError> errors = new ArrayList<>();
        PriceAggregator priceAggregator = new PriceAggregator();

        // 处理每个供应商的预览结果
        supplierResults.forEach(supplierResult -> {
            if (!supplierResult.isSuccess() || supplierResult.getResponse() == null) {
                addSupplierErrorToProductItems(supplierResult, productItems, errors);
                return;
            }
            // 处理API调用成功预览订单信息
            OrderPreviewResponse response = supplierResult.getResponse();
            if (!response.getSuccess()) {
                addApiErrorToProductItems(supplierResult, response, productItems, errors);
                return;
            }
            List<OrderPreviewResult> previewResultList = response.getOrderPreviewResult();
            if (CollectionUtils.isEmpty(previewResultList)) {
                addEmptyResultErrorToProductItems(supplierResult, productItems, errors);
                return;
            }

            // 订单预览结果有多个情况，需要循环处理。
            previewResultList.forEach(previewResult -> {
                processPreviewResult(supplierResult, previewResult, productItems, errors);
            });
            // 累计供应商的价格
            // 累加商品金额和运费
            priceAggregator.addMerchandiseAmount(supplierResult.getMerchandiseAmount());
            // 运费
            priceAggregator.addShippingAmount(supplierResult.getShippingAmount());
            log.info("供应商 {} 处理 {} 个预览(如果订单被拆开了, previewResultList 会包含多个结果)。 ", supplierResult.getSupplierId(), supplierResults.size());
        });
        // 构建价格详情
        OrderPreviewVO.PriceDetails priceDetails = buildPriceDetails(productItems, priceAggregator);
        // 构建预览概览
        OrderPreviewVO.OrderPreviewSummary summary = buildPreviewSummary(productItems, errors);
        // 返回前端 VO
        return OrderPreviewVO.builder()
            .orderPreviewSummary(summary)
            .productItems(productItems)
            .priceDetails(priceDetails)
            .errors(errors)
            .build();
    }

    /**
     * 处理预览结果
     */
    private void processPreviewResult(SupplierPreviewResult supplierResult,
        OrderPreviewResult previewResult, List<OrderPreviewVO.ProductItemPreview> productItems, List<OrderPreviewVO.OrderPreviewError> errors) {

        if (previewResult.getStatus() != null && !previewResult.getStatus()) {
            addPreviewResultErrorToProductItems(supplierResult, previewResult, productItems, errors);
            return;
        }

        List<CreateOrderPreviewResultCargoModel> cargoList = previewResult.getCargoList();
        if (CollectionUtils.isEmpty(cargoList)) {
            return;
        }

        List<OrderItemInfo> matchedItemList = Lists.newArrayList();
        for (CreateOrderPreviewResultCargoModel cargo : cargoList) {
            OrderItemInfo matchedItem = findMatchedItem(supplierResult.getItems(), cargo.getOfferId(),
                cargo.getSpecId());
            if (matchedItem == null) {
                log.warn("未找到匹配的商品项，offerId: {}, specId: {}", cargo.getOfferId(), cargo.getSpecId());
                continue;
            }

            if (hasCargoError(cargo)) {
                addCargoErrorToErrors(cargo, errors);
                addErrorProductItem(cargo, matchedItem, productItems);
            } else {
                // 添加成功商品项
                addSuccessProductItem(cargo, matchedItem, productItems);
                matchedItemList.add(matchedItem);
            }
        }
        // 客户需要支付商品总金额
        BigDecimal shippingAmount = fenToYuan(previewResult.getSumCarriage());
        BigDecimal merchandiseAmount = matchedItemList.stream()
            .map(OrderItemInfo::getSubTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalAmount = merchandiseAmount.add(shippingAmount);
        // 服务费
        BigDecimal serviceFee = getServiceFee(merchandiseAmount);

        // 设置当前供应商的 运费 和 总价、商品总价、服务费
        supplierResult.setShippingAmount(shippingAmount);
        // 设置商品总价
        supplierResult.setMerchandiseAmount(merchandiseAmount);
        // 设置服务费
        supplierResult.setServiceFee(serviceFee);
        // 设置总金额
        supplierResult.setTotalAmount(totalAmount);
        log.info("供应商 {} 处理商品 {} 个预览, 商品总价: {}, 运费: {}, 服务费: {}, 总价: {} ",
            supplierResult.getSupplierId(), matchedItemList.size(), merchandiseAmount, shippingAmount, serviceFee, totalAmount);
    }

    /**
     * 判断cargo是否包含错误
     */
    private boolean hasCargoError(CreateOrderPreviewResultCargoModel cargo) {
        String resultCode = cargo.getResultCode();
        return resultCode != null && !resultCode.equals("200") && !resultCode.equals("0")
            && !resultCode.equals("success");
    }

    /**
     * 添加成功商品项
     */
    private void addSuccessProductItem(CreateOrderPreviewResultCargoModel cargo,
        OrderItemInfo matchedItem,
        List<OrderPreviewVO.ProductItemPreview> productItems) {

        // 比较 sku 单价 ，如果 cargo 价格比 sku 价格高，则使用 cargo 价格。
        BigDecimal skuPrice = matchedItem.getSku().getPrice();
        // 预览价格
        BigDecimal unitPrice = doubleToBigDecimal(cargo.getFinalUnitPrice());
        if (unitPrice.compareTo(skuPrice) > 0) {
            log.debug("SKU {} 使用预览价格 {} 替代原价格 {}", matchedItem.getSku().getId(), unitPrice, skuPrice);
        } else {
            unitPrice = skuPrice;
        }
        // 转换为美元
        BigDecimal unitPriceUsd = CurrencyConversionUtils.cnyToUsd(unitPrice);
        // 行总金额
        BigDecimal lineTotalAmount = unitPrice.multiply(BigDecimal.valueOf(matchedItem.getQuantity()));
        // 转换为美元
        BigDecimal lineTotalAmountUsd = CurrencyConversionUtils.cnyToUsd(lineTotalAmount);

        // 商品小计
        matchedItem.setSubTotal(lineTotalAmount);

        OrderPreviewVO.ProductItemPreview productItem = OrderPreviewVO.ProductItemPreview.builder()
            .spuId(matchedItem.getSpu().getId())
            .specId(matchedItem.getSku().getPlatformSpecId())
            .skuId(matchedItem.getSku().getId())
            .productTitle(matchedItem.getSpu().getTitle())
            .productTitleEn(matchedItem.getSpu().getTitleTrans())
            .productImageUrl(matchedItem.getSpu().getMainImage())
            .skuImage(matchedItem.getSku().getImage())
            .skuSpecs(matchedItem.getSku().getSpecs())
            .unitPrice(unitPrice)
            .unitPriceUsd(unitPriceUsd != null ? unitPriceUsd : BigDecimal.ZERO)
            .orderedQuantity(matchedItem.getQuantity())
            .lineTotalAmount(lineTotalAmount)
            .lineTotalAmountUsd(lineTotalAmountUsd != null ? lineTotalAmountUsd : BigDecimal.ZERO)
            .unitOfMeasure(getUnitOfMeasure(matchedItem.getSku()))
            .available(true)
            .message("商品可用")
            .build();

        productItems.add(productItem);
    }

    /**
     * 添加错误商品项
     */
    private void addErrorProductItem(CreateOrderPreviewResultCargoModel cargo,
        OrderItemInfo matchedItem,
        List<OrderPreviewVO.ProductItemPreview> productItems) {
        OrderPreviewVO.ProductItemPreview productItem = OrderPreviewVO.ProductItemPreview.builder()
            .spuId(matchedItem.getSpu().getId())
            .specId(matchedItem.getSku().getPlatformSpecId())
            .skuId(matchedItem.getSku().getId())
            .productTitle(matchedItem.getSpu().getTitle())
            .productTitleEn(matchedItem.getSpu().getTitleTrans())
            .productImageUrl(matchedItem.getSpu().getMainImage())
            .skuImage(matchedItem.getSku().getImage())
            .skuSpecs(matchedItem.getSku().getSpecs())
            .unitPrice(BigDecimal.ZERO)
            .unitPriceUsd(BigDecimal.ZERO)
            .orderedQuantity(matchedItem.getQuantity())
            .lineTotalAmount(BigDecimal.ZERO)
            .lineTotalAmountUsd(BigDecimal.ZERO)
            .unitOfMeasure(getUnitOfMeasure(matchedItem.getSku()))
            .available(false)
            .message(OrderErrorCodeUtil.getFriendlyMessage(cargo.getResultCode()))
            .build();

        productItems.add(productItem);
    }

    /**
     * 添加供应商错误到商品项
     */
    private void addSupplierErrorToProductItems(SupplierPreviewResult supplierResult,
        List<OrderPreviewVO.ProductItemPreview> productItems,
        List<OrderPreviewVO.OrderPreviewError> errors) {
        errors.add(OrderPreviewVO.OrderPreviewError.builder()
            .errorCode("SUPPLIER_ERROR")
            .errorMessage(supplierResult.getErrorMessage() != null ? supplierResult.getErrorMessage() : "供应商服务异常")
            .errorType("SUPPLIER")
            .build());
        supplierResult.getItems().forEach(item -> addUnavailableProductItem(item, productItems, "供应商服务异常，商品暂时不可购买"));
    }

    /**
     * 添加API错误到商品项
     */
    private void addApiErrorToProductItems(SupplierPreviewResult supplierResult, OrderPreviewResponse response,
        List<OrderPreviewVO.ProductItemPreview> productItems, List<OrderPreviewVO.OrderPreviewError> errors) {
        String errorMessage = OrderErrorCodeUtil.getFriendlyMessage(response.getErrorCode());
        errors.add(OrderPreviewVO.OrderPreviewError.builder()
            .errorCode(response.getErrorCode())
            .errorMessage(errorMessage)
            .errorType("API")
            .build());
        supplierResult.getItems().forEach(item -> addUnavailableProductItem(item, productItems, errorMessage));
    }

    /**
     * 添加空结果错误到商品项
     */
    private void addEmptyResultErrorToProductItems(SupplierPreviewResult supplierResult,
        List<OrderPreviewVO.ProductItemPreview> productItems,
        List<OrderPreviewVO.OrderPreviewError> errors) {
        errors.add(OrderPreviewVO.OrderPreviewError.builder()
            .errorCode("EMPTY_RESULT")
            .errorMessage("预览结果为空")
            .errorType("API")
            .build());
        supplierResult.getItems().forEach(item -> addUnavailableProductItem(item, productItems, "预览失败，请稍后重试"));
    }

    /**
     * 添加预览结果错误到商品项
     */
    private void addPreviewResultErrorToProductItems(SupplierPreviewResult supplierResult,
        OrderPreviewResult previewResult,
        List<OrderPreviewVO.ProductItemPreview> productItems,
        List<OrderPreviewVO.OrderPreviewError> errors) {
        String errorMessage = previewResult.getMessage() != null ? previewResult.getMessage() : "预览失败";
        errors.add(OrderPreviewVO.OrderPreviewError.builder()
            .errorCode(previewResult.getResultCode())
            .errorMessage(errorMessage)
            .errorType("PREVIEW")
            .build());
        supplierResult.getItems().forEach(item -> addUnavailableProductItem(item, productItems, errorMessage));
    }

    /**
     * 添加cargo错误到错误列表
     */
    private void addCargoErrorToErrors(CreateOrderPreviewResultCargoModel cargo, List<OrderPreviewVO.OrderPreviewError> errors) {
        String friendlyMessage = OrderErrorCodeUtil.getFriendlyMessage(cargo.getResultCode());
        errors.add(OrderPreviewVO.OrderPreviewError.builder()
            .errorCode(cargo.getResultCode())
            .errorMessage(friendlyMessage)
            .offerId(cargo.getOfferId())
            .specId(cargo.getSpecId())
            .errorType("PRODUCT")
            .build());
    }

    /**
     * 添加不可用商品项
     */
    private void addUnavailableProductItem(OrderItemInfo item, List<OrderPreviewVO.ProductItemPreview> productItems, String message) {
        OrderPreviewVO.ProductItemPreview productItem = OrderPreviewVO.ProductItemPreview.builder()
            .spuId(item.getSpu().getId())
            .specId(item.getSku().getPlatformSpecId())
            .skuId(item.getSku().getId())
            .productTitle(item.getSpu().getTitle())
            .productTitleEn(item.getSpu().getTitleTrans())
            .productImageUrl(item.getSpu().getMainImage())
            .skuImage(item.getSku().getImage())
            .skuSpecs(item.getSku().getSpecs())
            .unitPrice(BigDecimal.ZERO)
            .unitPriceUsd(BigDecimal.ZERO)
            .orderedQuantity(item.getQuantity())
            .lineTotalAmount(BigDecimal.ZERO)
            .lineTotalAmountUsd(BigDecimal.ZERO)
            .unitOfMeasure(getUnitOfMeasure(item.getSku()))
            .available(false)
            .message(message)
            .build();
        productItems.add(productItem);
    }

    /**
     * 构建价格详情
     */
    private OrderPreviewVO.PriceDetails buildPriceDetails(List<ProductItemPreview> productItems, PriceAggregator aggregator) {
        // 2025年07月09日10:26:36 处理的价格计算里，不需要优惠金额提供。 客户购买价格优惠金额不需要提现。真实买家是 Fulfillmen
        BigDecimal merchandiseAmount = aggregator.getMerchandiseAmount();
        BigDecimal shippingAmount = aggregator.getShippingAmount();
        BigDecimal serviceFee = getServiceFee(merchandiseAmount);
        BigDecimal serviceFeeRate = getServiceFeeRate();
        BigDecimal totalAmount = merchandiseAmount.add(shippingAmount).add(serviceFee);

        BigDecimal merchandiseAmountUsd = productItems.stream().map(ProductItemPreview::getLineTotalAmountUsd).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal shippingAmoundUsd = NumberUtil.nullToZero(CurrencyConversionUtils.cnyToUsd(shippingAmount, false));
        BigDecimal serviceFeeUsd = NumberUtil.nullToZero(CurrencyConversionUtils.cnyToUsd(serviceFee));
        BigDecimal totalAmountUsd = merchandiseAmountUsd.add(shippingAmoundUsd).add(serviceFeeUsd);

        log.info("构建价格详情: 商品金额: {} usd:{}, 运费: {} usd:{}, 服务费: {} usd:{}, 总金额: {} usd:{}",
            merchandiseAmount, merchandiseAmountUsd, shippingAmount, shippingAmoundUsd, serviceFee, serviceFeeUsd, totalAmount, totalAmountUsd);
        return OrderPreviewVO.PriceDetails.builder()
            .merchandiseAmount(merchandiseAmount)
            .merchandiseAmountUsd(merchandiseAmountUsd)
            .shippingAmount(shippingAmount)
            // 如果运费 0 则就是 0
            .shippingAmountUsd(shippingAmoundUsd)
            .serviceFee(serviceFee)
            .serviceFeeUsd(serviceFeeUsd)
            .serviceFeeRate(serviceFeeRate)
            .totalAmount(totalAmount)
            .totalAmountUsd(totalAmountUsd)
            .build();
    }

    /**
     * 获取服务费 根据金额计算
     * 后期废弃，不再收取服务费。
     * <pre>
     * 优先获取
     * 1. wms 用户类型服务费率
     * 2. 租户服务费率
     * 3. 系统默认服务费率 15%
     * </pre>
     */
    @NotNull
    @Deprecated
    private BigDecimal getServiceFee(BigDecimal merchandiseAmount) {
        BigDecimal serviceFeeRate = getServiceFeeRate();
        return merchandiseAmount.multiply(serviceFeeRate).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 获取服务费费率
     */
    @NotNull
    private BigDecimal getServiceFeeRate() {
        // 1. 根据 wms 用户类型获取，如果 wms 用户类型，则获取 wms 设定的服务费。
        BigDecimal wmsServiceFee = UserContextHolder.getWmsServiceFee();
        if (wmsServiceFee != null && wmsServiceFee.compareTo(BigDecimal.ZERO) > 0) {
            // FIXBUG: 2025/8/12 优先使用 wms 用户设定的服务费费率来处理 8 / 100 = 0.08
            return NumberUtil.div(wmsServiceFee, new BigDecimal("100"));
        }
        // 2. 获取租户服务费率
        BigDecimal tenantServiceFee = BigDecimal.ZERO;
        EnhancedTenantContext enhancedTenantContext = EnhancedTenantContextHolder.getEnhancedTenantContext().orElse(null);
        if (enhancedTenantContext != null && enhancedTenantContext.getDetailInfo() != null) {
            // 获取租户服务费率
            tenantServiceFee = enhancedTenantContext.getDetailInfo().getServiceFee() != null ? BigDecimal.valueOf(enhancedTenantContext.getDetailInfo().getServiceFee()).divide(new BigDecimal("100"), 2,
              RoundingMode.HALF_UP) : BigDecimal.ZERO;
        }
        // 3. 如果租户服务费率为 0，则使用系统默认服务费率 15%
        return tenantServiceFee.compareTo(BigDecimal.ZERO) > 0 ? tenantServiceFee : SERVICE_FEE_RATE;
    }

    /**
     * 构建预览概览
     */
    private OrderPreviewVO.OrderPreviewSummary buildPreviewSummary(List<OrderPreviewVO.ProductItemPreview> productItems,
        List<OrderPreviewVO.OrderPreviewError> errors) {
        int totalQuantity = productItems.stream().mapToInt(OrderPreviewVO.ProductItemPreview::getOrderedQuantity).sum();
        boolean hasErrors = !errors.isEmpty();
        boolean success = !productItems.isEmpty()
            && productItems.stream().anyMatch(OrderPreviewVO.ProductItemPreview::getAvailable);
        return OrderPreviewVO.OrderPreviewSummary.builder()
            .totalQuantity(totalQuantity)
            .productTypeCount(productItems.size())
            .success(success)
            .hasErrors(hasErrors)
            .build();
    }

    /**
     * 查找匹配的商品项
     *
     * @param items   商品项
     * @param offerId offerId
     * @param specId  specId
     * @return 匹配的商品项
     */
    private OrderItemInfo findMatchedItem(List<OrderItemInfo> items, Long offerId, String specId) {
        return items.stream()
            .filter(item -> {
                boolean isSingleItem = item.getSpu().getIsSingleItem() == TzProductSpuSingleItemEnum.YES;
                // 单品情况下，specId 可能为空
                return Objects.equals(item.getSku().getPlatformProductId(), offerId.toString())
                    && (Objects.equals(item.getSku().getPlatformSpecId(), specId) || isSingleItem);
            })
            .findFirst()
            .orElse(null);
    }

    /**
     * 获取计量单位
     */
    private String getUnitOfMeasure(TzProductSku sku) {
        return sku.getUnitInfo() != null ? sku.getUnitInfo().getUnit() : "件";
    }

    /**
     * 构建空订单预览VO
     */
    private OrderPreviewVO buildEmptyOrderPreviewVO() {
        return OrderPreviewVO.builder()
            .orderPreviewSummary(OrderPreviewVO.OrderPreviewSummary.builder()
                .totalQuantity(0).productTypeCount(0).success(false).hasErrors(false).build())
            .productItems(new ArrayList<>())
            .priceDetails(OrderPreviewVO.PriceDetails.builder()
                .merchandiseAmount(BigDecimal.ZERO).merchandiseAmountUsd(BigDecimal.ZERO)
                .shippingAmount(BigDecimal.ZERO).shippingAmountUsd(BigDecimal.ZERO)
                .serviceFee(BigDecimal.ZERO).serviceFeeUsd(BigDecimal.ZERO)
                .totalAmount(BigDecimal.ZERO).totalAmountUsd(BigDecimal.ZERO)
                .build())
            .errors(new ArrayList<>())
            .build();
    }

    /**
     * 分转换为元
     */
    private BigDecimal fenToYuan(Long fen) {
        if (fen == null) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(fen).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
    }

    /**
     * 双精度转换为BigDecimal
     */
    private BigDecimal doubleToBigDecimal(Double value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(value).setScale(2, RoundingMode.HALF_UP);
    }

    @Getter
    private static class PriceAggregator {

        private BigDecimal merchandiseAmount = BigDecimal.ZERO;
        private BigDecimal merchandiseAmountUsd = BigDecimal.ZERO;
        private BigDecimal shippingAmount = BigDecimal.ZERO;
        private BigDecimal shippingAmountUsd = BigDecimal.ZERO;
        private BigDecimal discountAmount = BigDecimal.ZERO;
        private BigDecimal discountAmountUsd = BigDecimal.ZERO;

        public void addMerchandiseAmount(BigDecimal amount) {
            this.merchandiseAmount = this.merchandiseAmount.add(NumberUtil.nullToZero(amount));
        }

        public void addMerchandiseAmountUsd(BigDecimal amount) {
            this.merchandiseAmountUsd = this.merchandiseAmountUsd.add(NumberUtil.nullToZero(amount));
        }

        public void addShippingAmount(BigDecimal amount) {
            this.shippingAmount = this.shippingAmount.add(NumberUtil.nullToZero(amount));
        }

        public void addShippingAmountUsd(BigDecimal amount) {
            this.shippingAmountUsd = this.shippingAmountUsd.add(NumberUtil.nullToZero(amount));
        }

        public void addDiscountAmount(BigDecimal amount) {
            this.discountAmount = this.discountAmount.add(NumberUtil.nullToZero(amount));
        }

        public void addDiscountAmountUsd(BigDecimal amount) {
            this.discountAmountUsd = this.discountAmountUsd.add(NumberUtil.nullToZero(amount));
        }

    }

    @Data
    @Builder
    public static class SupplierPreviewResult {

        /**
         * 供应商 ID
         */
        private String supplierId;
        /**
         * 供应商名称
         */
        private String supplierName;
        /**
         * 商品项
         */
        private List<OrderItemInfo> items;
        /**
         * 预览响应
         */
        private OrderPreviewResponse response;
        /**
         * 服务费
         */
        private BigDecimal serviceFee;
        /**
         * 运费
         */
        private BigDecimal shippingAmount;
        /**
         * 商品金额
         */
        private BigDecimal merchandiseAmount;
        /**
         * 总金额
         */
        private BigDecimal totalAmount;
        /**
         * 预览是否成功
         */
        private boolean success;
        /**
         * 错误消息
         */
        private String errorMessage;
    }
}
