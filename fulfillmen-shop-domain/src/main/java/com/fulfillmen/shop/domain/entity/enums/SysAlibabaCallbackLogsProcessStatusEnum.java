/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 阿里巴巴回调日志处理状态枚举.
 *
 * <AUTHOR>
 * @date 2025/8/13 10:18
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum SysAlibabaCallbackLogsProcessStatusEnum {

    /**
     * 未处理
     */
    UN_PROCESSED(0, "否"),

    /**
     * 已处理
     */
    PROCESSED(1, "是");

    @EnumValue
    @JsonValue
    private final int value;

    /**
     * 描述
     */
    private final String desc;

    SysAlibabaCallbackLogsProcessStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
