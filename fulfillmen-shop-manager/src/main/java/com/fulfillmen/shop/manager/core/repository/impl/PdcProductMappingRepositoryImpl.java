/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.enums.FulfillmenValidationCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.common.util.MetaInfoHashUtils;
import com.fulfillmen.shop.dao.mapper.PdcProductMappingMapper;
import com.fulfillmen.shop.domain.convert.PdcProductConvertMapping;
import com.fulfillmen.shop.domain.convert.product.PdcProductDetailConvertMapping;
import com.fulfillmen.shop.domain.convert.product.PdcProductRecommendConvertMapping;
import com.fulfillmen.shop.domain.convert.product.PdcProductSearchConvertMapping;
import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.dto.ProductInfoDTO;
import com.fulfillmen.shop.domain.dto.ProductSearchRequestDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.PdcProductMapping;
import com.fulfillmen.shop.domain.entity.enums.PdcProductMappingSyncStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.domain.req.AggregateSearchReq;
import com.fulfillmen.shop.domain.req.AggregateSearchReq.SearchType;
import com.fulfillmen.shop.manager.config.ProductSyncConfig;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.manager.event.ProductDataSyncEvent;
import com.fulfillmen.shop.manager.support.alibaba.IProductManager;
import com.fulfillmen.shop.manager.support.alibaba.IToolsManager;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsDetailResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsImageSearchResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsRecommendResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsRelatedRecommendResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSearchResponse;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSearchResponse.GoodsInfo;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsSellerResponse;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.ahoo.cosid.IdGenerator;
import me.ahoo.cosid.provider.DefaultIdGeneratorProvider;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * 商品映射仓储实现类
 *
 * <pre>
 * 职责：
 * 1. 集中管理所有商品数据的数据库操作
 * 2. 提供缓存→数据库→API的三级数据获取策略
 * 3. 处理批量数据同步操作
 * 4. 统一的商品数据访问入口
 *
 * 后续可以优化的地方：
 * 1. 缓存预热 - 可以在系统启动时或定时任务中调用
 * 2. 清除搜索缓存 - 可以在商品数据更新时调用
 * 3. 缓存命中率统计 - 可以集成到更完善的监控系统
 * 4. 缓存失效策略 - 可以基于数据更新时间和搜索频次进行智能失效
 * 5. 缓存预热策略 - 可以预加载热门搜索条件
 * 6. 缓存降级策略 - 可以自动降级到直接搜索
 * 7. 缓存监控系统 - 可以集成到更完善的监控系统
 * 8. 缓存失效策略 - 可以基于数据更新时间和搜索频次进行智能失效
 * 9. 缓存预热策略 - 可以预加载热门搜索条件
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/5/29 23:27
 * @since 1.0.0
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class PdcProductMappingRepositoryImpl extends CrudRepository<PdcProductMappingMapper, PdcProductMapping> implements PdcProductMappingRepository, InitializingBean {

    private final CacheManager cacheManager;
    private final IProductManager productManager;
    private final IToolsManager toolsManager;
    private final ProductSyncConfig productSyncConfig;
    private final PdcProductConvertMapping pdcProductConvertMapping;
    private final PdcProductDetailConvertMapping pdcProductDetailConvertMapping;
    private final PdcProductSearchConvertMapping pdcProductSearchConvertMapping;
    private final PdcProductRecommendConvertMapping pdcProductRecommendConvertMapping;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /**
     * 🎯 事件发布器，用于发布产品同步事件 替代直接依赖 ProductSyncService，解决循环依赖问题
     */
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 自动同步功能开关 从ProductSyncConfig获取配置
     */
    private boolean autoSyncEnabled;

    /**
     * 商品详情缓存
     */
    private Cache<Long, PdcProductMapping> productDetailCache;

    /**
     * 商品搜索结果缓存
     *
     * <pre>
     * key: 搜索条件组合字符串 hash md5
     * value: 商品映射列表
     * </pre>
     */
    private Cache<String, PageDTO<ProductInfoDTO>> productSearchCache;

    @Override
    public AlibabaProductDetailDTO getProductDetailWithCache(@Nonnull final Long id, @Nullable final Boolean isForceRefresh) {
        PdcProductMapping productMapping = getPdcProductMappingWithCache(id, isForceRefresh);
        if (productMapping != null) {
            // 使用转换器将 PdcProductMapping 转换为 AlibabaProductDetailDTO
            return pdcProductDetailConvertMapping.convertToAlibabaProductDetailDTO(productMapping);
        }
        return null;
    }

    /**
     * 从缓存或数据库获取商品详情
     *
     * <pre>
     * 优先从缓存获取，如果缓存不存在则从数据库获取，如果数据库不存在则从API获取并同步到DB和缓存
     * </pre>
     *
     * @param id             商品ID
     * @param isForceRefresh 是否强制刷新缓存 - 如果为true则忽略缓存直接从数据库获取 , default false
     * @return 商品详情
     */
    private PdcProductMapping getPdcProductMappingWithCache(Long id, Boolean isForceRefresh) {
        // 1. 强制刷新则跳过缓存获取
        if (Objects.nonNull(isForceRefresh) && !isForceRefresh) {
            PdcProductMapping cached = productDetailCache.get(id);
            if (cached != null) {
                log.debug("从缓存获取商品详情, id: {}", id);
                return cached;
            }
        }

        // 2. 查询数据库
        // 2.1 根据 id 查询
        PdcProductMapping dbRecord = this.baseMapper.selectById(id);
        if (dbRecord == null) {
            // 如果主键查询不存在，则通过 offerId 获取查询一次
            LambdaQueryWrapper<PdcProductMapping> queryWrapper = new LambdaQueryWrapper<>();
            // 2.2 1688平台商品ID 唯一键 platform_code + platform_product_id
            queryWrapper.eq(PdcProductMapping::getPlatformCode, PlatformCodeEnum.PLATFORM_CODE_1688)
                .eq(PdcProductMapping::getPlatformProductId, id);
            dbRecord = this.baseMapper.selectOne(queryWrapper);
        }
        // 如果数据库存在，并且是已同步状态、非强制刷新
        if (dbRecord != null && (dbRecord.getIsSynced() == PdcProductMappingSyncStatusEnum.SYNCED && !isForceRefresh)) {
            log.debug("从数据库获取商品详情, id: {}", id);
            // 更新缓存
            productDetailCache.put(id, dbRecord);
            return dbRecord;
        }

        // 3. 数据库不存在，从API获取并同步到DB
        log.debug("数据库不存在，从API获取商品详情, id: {}", id);
        return fetchFromApiAndSync(id, dbRecord);
    }

    /**
     * 从API获取商品详情并同步到数据库
     *
     * @param offerId  商品ID
     * @param dbRecord 数据库记录（可能为null）
     * @return 商品详情
     */
    private PdcProductMapping fetchFromApiAndSync(final Long offerId, final PdcProductMapping dbRecord) {
        try {
            // 有数据库记录则使用数据库记录的 offerId
            final boolean isExists = dbRecord != null;
            Long platformProductId = null;
            if (isExists) {
                platformProductId = Long.valueOf(dbRecord.getPlatformProductId());
                log.debug("从API获取商品详情, offerId: {} , platformProductId: {}", offerId, platformProductId);
            }
            // 从API获取商品详情 - 这是关键路径，必须同步完成
            GoodsDetailResponse.ProductDetail productDetail = productManager.getProductDetail(isExists ? platformProductId : offerId);
            if (productDetail == null) {
                log.error("找不到商品详情, offerId: {}", offerId);
                return null;
            }
            // CompletableFuture 异步获取旺旺昵称
            CompletableFuture<String> sellerOpenIdsFuture = CompletableFuture.supplyAsync(() -> toolsManager.getWangWangNickname(productDetail.getSellerOpenId()),
                threadPoolTaskExecutor);

            // 创建或更新商品映射
            PdcProductMapping pdcProductMapping;
            if (isExists) {
                // 重新同步并更新
                pdcProductMapping = pdcProductDetailConvertMapping.createPdcProductMappingByProductDetail1688(productDetail, dbRecord.getId());
                // 更新统计信息
                pdcProductMapping.setAccessCount((dbRecord.getAccessCount() == null ? 0 : dbRecord.getAccessCount()) + 1);
                pdcProductMapping.setDetailViewCount((dbRecord.getDetailViewCount() == null ? 0 : dbRecord.getDetailViewCount()) + 1);
                pdcProductMapping.setFirstAccessTime(dbRecord.getFirstAccessTime() == null ? LocalDateTime.now() : dbRecord.getFirstAccessTime());
                pdcProductMapping.setLastAccessTime(LocalDateTime.now());
            } else {
                // 创建新的商品映射
                pdcProductMapping = pdcProductDetailConvertMapping.createPdcProductMappingByProductDetail1688(productDetail, null);
            }
            // 映射 hashcode 数据映射信息
            pdcProductMapping.setMetaInfoHash(MetaInfoHashUtils.calculateMetaInfoHash(pdcProductMapping));

            // 同步到数据库 - 关键操作，必须同步完成以确保数据一致性
            // 设置旺旺昵称
            pdcProductMapping.setPlatformProductSellerOpenIdDecrypt(sellerOpenIdsFuture.get());
            this.baseMapper.insertOrUpdate(pdcProductMapping);
            log.debug("商品详情同步到数据库完成, id: {}", offerId);

            // 更新缓存
            productDetailCache.put(offerId, pdcProductMapping);
            log.debug("商品详情更新到缓存完成, id: {}", offerId);

            // 🎯 Phase 1.3: 触发自动同步到TzProduct表
            triggerAutoSyncWithEventType(Collections.singletonList(String.valueOf(offerId)), ProductDataSyncEvent.SyncEventType.PRODUCT_DETAIL_FETCHED, "产品详情获取后同步");

            return pdcProductMapping;
        } catch (Exception e) {
            log.error("从API获取并同步商品详情失败, id: {}", offerId, e);
            return null;
        }
    }

    // ======================== 批量同步方法 ========================

    /**
     * 批量同步搜索结果中的商品数据 - 优化版本
     *
     * <pre>
     * 优化特性：
     * 1. 批处理大小控制：避免大数据量导致的内存问题
     * 2. 事务管理：确保数据一致性
     * 3. 并行处理：提高对象转换性能
     * 4. 性能监控：详细的执行时间统计
     * 5. 异常处理：完善的错误处理和回滚机制
     * 6. 内存优化：减少不必要的对象创建
     * </pre>
     *
     * @param goodsInfoList 1688搜索结果商品列表
     * @return 同步后的商品映射列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PdcProductMapping> syncSearchResultData(List<GoodsInfo> goodsInfoList) {
        if (CollectionUtil.isEmpty(goodsInfoList)) {
            return ListUtil.empty();
        }

        // 性能统计开始
        long startTime = System.currentTimeMillis();
        log.info("开始批量同步搜索结果商品数据，数量: {}", goodsInfoList.size());

        try {
            // 大数据量分批处理，每批最多500条
            final int BATCH_SIZE = 500;
            if (goodsInfoList.size() > BATCH_SIZE) {
                return processBatchSync(goodsInfoList, BATCH_SIZE, startTime);
            }

            // 小数据量直接处理
            return processSingleBatch(goodsInfoList, startTime);

        } catch (Exception e) {
            log.error("批量同步搜索结果商品数据失败，数量: {}", goodsInfoList.size(), e);
            throw new RuntimeException("商品数据同步失败: " + e.getMessage(), e);
        }
    }

    /**
     * 分批处理大数据量同步
     */
    private List<PdcProductMapping> processBatchSync(List<GoodsInfo> goodsInfoList, int batchSize, long startTime) {
        List<PdcProductMapping> allResults = new ArrayList<>();
        int totalBatches = (goodsInfoList.size() + batchSize - 1) / batchSize;

        log.info("大数据量分批处理，总数: {}, 批次大小: {}, 总批次: {}", goodsInfoList.size(), batchSize, totalBatches);

        for (int i = 0; i < goodsInfoList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, goodsInfoList.size());
            List<GoodsInfo> batch = goodsInfoList.subList(i, endIndex);

            int currentBatch = (i / batchSize) + 1;
            long batchStartTime = System.currentTimeMillis();

            try {
                List<PdcProductMapping> batchResults = processSingleBatch(batch, batchStartTime);
                allResults.addAll(batchResults);

                long batchDuration = System.currentTimeMillis() - batchStartTime;
                log.debug("批次 {}/{} 处理完成，数量: {}, 耗时: {}ms", currentBatch, totalBatches, batch.size(), batchDuration);

            } catch (Exception e) {
                log.error("批次 {}/{} 处理失败，数量: {}", currentBatch, totalBatches, batch.size(), e);
                throw new RuntimeException("批次处理失败: " + e.getMessage(), e);
            }
        }

        long totalDuration = System.currentTimeMillis() - startTime;
        log.info("所有批次处理完成，总数: {}, 总耗时: {}ms, 平均每条: {}ms", allResults.size(), totalDuration, allResults.isEmpty() ? 0 : totalDuration / allResults.size());

        return allResults;
    }

    /**
     * 处理单个批次的商品同步
     */
    private List<PdcProductMapping> processSingleBatch(List<GoodsInfo> goodsInfoList, long startTime) {
        // 1. 提取平台商品ID列表 - 使用并行流优化
        long extractStartTime = System.currentTimeMillis();
        List<String> platformProductIds = goodsInfoList.parallelStream().map(goods -> String.valueOf(goods.getOfferId())).toList();
        log.debug("提取商品ID完成，耗时: {}ms", System.currentTimeMillis() - extractStartTime);

        // 2. 一次性查询已存在的商品数据
        long queryStartTime = System.currentTimeMillis();
        Map<String, PdcProductMapping> existingDataMap = getExistingProductMappingsOptimized(platformProductIds);
        log.debug("查询已存在数据完成，耗时: {}ms，已存在: {} 条", System.currentTimeMillis() - queryStartTime, existingDataMap.size());

        // 3. 并行处理对象转换和分类
        long processStartTime = System.currentTimeMillis();
        ProcessResult processResult = processGoodsInfoParallel(goodsInfoList, existingDataMap);
        log.debug("数据处理完成，耗时: {}ms，新增: {} 条，更新: {} 条", System.currentTimeMillis() - processStartTime, processResult.insertList.size(),
            processResult.updateList.size());

        // 4. 批量执行数据库操作
        long dbStartTime = System.currentTimeMillis();
        int affectedRows = executeBatchDatabaseOperations(processResult);
        log.debug("数据库操作完成，耗时: {}ms，影响行数: {}", System.currentTimeMillis() - dbStartTime, affectedRows);

        // 5. 性能统计
        long totalDuration = System.currentTimeMillis() - startTime;
        log.info("单批次同步完成，总数: {}, 影响: {} 条，总耗时: {}ms", goodsInfoList.size(), affectedRows, totalDuration);

        // 🎯 Phase 1.3: 批量同步后触发自动同步到TzProduct表
        List<String> productIds = processResult.resultList.stream().map(PdcProductMapping::getPlatformProductId).collect(Collectors.toList());
        triggerAutoSyncWithEventType(productIds, ProductDataSyncEvent.SyncEventType.SEARCH_RESULT_SYNCED, "搜索结果同步后触发");

        return processResult.resultList;
    }

    /**
     * 并行处理商品信息转换和分类
     */
    private ProcessResult processGoodsInfoParallel(List<GoodsInfo> goodsInfoList, Map<String, PdcProductMapping> existingDataMap) {

        // 使用并行流处理对象转换
        List<ProductMappingTask> tasks = goodsInfoList.parallelStream().map(goodsInfo -> createProductMappingTask(goodsInfo, existingDataMap)).toList();

        // 分离插入和更新列表
        List<PdcProductMapping> insertList = new ArrayList<>();
        List<PdcProductMapping> updateList = new ArrayList<>();
        List<PdcProductMapping> resultList = new ArrayList<>();

        for (ProductMappingTask task : tasks) {
            if (task.isUpdate()) {
                updateList.add(task.getProductMapping());
            } else {
                insertList.add(task.getProductMapping());
            }
            resultList.add(task.getProductMapping());
        }

        return new ProcessResult(insertList, updateList, resultList);
    }

    /**
     * 创建产品映射任务
     */
    private ProductMappingTask createProductMappingTask(GoodsSearchResponse.GoodsInfo goodsInfo, Map<String, PdcProductMapping> existingDataMap) {
        String platformProductId = String.valueOf(goodsInfo.getOfferId());
        PdcProductMapping existingRecord = existingDataMap.get(platformProductId);

        PdcProductMapping productMapping;
        boolean isUpdate = Objects.nonNull(existingRecord);

        if (Objects.nonNull(existingRecord)) {
            // 更新现有记录：保留原ID，更新数据
            productMapping = pdcProductSearchConvertMapping.createPdcProductMappingFromGoodsInfo(goodsInfo, existingRecord.getId());

            // 保留重要的统计信息 - 使用更安全的方式
            preserveStatisticsData(productMapping, existingRecord);
        } else {
            // 新增记录：生成新ID
            productMapping = pdcProductSearchConvertMapping.createPdcProductMappingFromGoodsInfo(goodsInfo, null);
        }

        return new ProductMappingTask(productMapping, isUpdate);
    }

    /**
     * 保留统计数据 - 防止空指针异常
     */
    private void preserveStatisticsData(PdcProductMapping newMapping, PdcProductMapping existingRecord) {
        newMapping.setAccessCount(existingRecord.getAccessCount() != null ? existingRecord.getAccessCount() : 0);
        newMapping.setSearchCount(existingRecord.getSearchCount() != null ? existingRecord.getSearchCount() : 0);
        newMapping.setDetailViewCount(existingRecord.getDetailViewCount() != null ? existingRecord.getDetailViewCount() : 0);
        newMapping.setInteractionCount(existingRecord.getInteractionCount() != null ? existingRecord.getInteractionCount() : 0);
        newMapping.setFirstAccessTime(existingRecord.getFirstAccessTime() != null ? existingRecord.getFirstAccessTime() : LocalDateTime.now());
    }

    /**
     * 执行批量数据库操作
     */
    private int executeBatchDatabaseOperations(ProcessResult processResult) {
        int affectedRows = 0;

        // 批量插入
        if (CollectionUtil.isNotEmpty(processResult.insertList)) {
            try {
                int insertCount = this.baseMapper.batchInsertProducts(processResult.insertList);
                affectedRows += insertCount;
                log.debug("批量插入成功，预期: {} 条，实际: {} 条", processResult.insertList.size(), insertCount);
            } catch (Exception e) {
                log.error("批量插入操作失败", e);
                throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.BATCH_INSERT_FAILED, e);
            }
        }

        // 批量更新
        if (CollectionUtil.isNotEmpty(processResult.updateList)) {
            try {
                int updateCount = this.baseMapper.batchUpdateProducts(processResult.updateList);
                affectedRows += updateCount;
                log.debug("批量更新成功，预期: {} 条，实际: {} 条", processResult.updateList.size(), updateCount);
            } catch (Exception e) {
                log.error("批量更新操作失败", e);
                throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.BATCH_UPDATE_FAILED, e);
            }
        }

        return affectedRows;
    }

    /**
     * 优化的查询已存在商品数据方法 减少不必要的Optional包装和流操作
     */
    private Map<String, PdcProductMapping> getExistingProductMappingsOptimized(List<String> platformProductIds) {
        if (CollectionUtil.isEmpty(platformProductIds)) {
            return new HashMap<>();
        }

        LambdaQueryWrapper<PdcProductMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PdcProductMapping::getPlatformCode, PlatformCodeEnum.PLATFORM_CODE_1688).in(PdcProductMapping::getPlatformProductId, platformProductIds);

        List<PdcProductMapping> existingList = this.list(queryWrapper);
        if (CollectionUtil.isEmpty(existingList)) {
            return new HashMap<>();
        }

        // 直接构建Map，避免Optional包装
        Map<String, PdcProductMapping> resultMap = new HashMap<>(existingList.size());
        for (PdcProductMapping mapping : existingList) {
            resultMap.put(mapping.getPlatformProductId(), mapping);
        }
        return resultMap;
    }

    /**
     * 批量同步图片搜索结果中的商品数据 - 优化版本
     */
    @Override
    public List<PdcProductMapping> syncImageSearchResultData(List<GoodsImageSearchResponse.GoodsInfo> goodsInfoList) {
        if (CollectionUtil.isEmpty(goodsInfoList)) {
            return Collections.emptyList();
        }

        log.debug("开始批量同步图片搜索结果商品数据，数量: {}", goodsInfoList.size());

        // 1. 提取平台商品ID列表
        List<String> platformProductIds = goodsInfoList.stream().map(goods -> String.valueOf(goods.getOfferId())).toList();

        // 2. 一次性查询已存在的商品数据
        Map<String, PdcProductMapping> existingDataMap = getExistingProductMappingsOptimized(platformProductIds);

        // 3. 分离新增和更新数据
        List<PdcProductMapping> insertList = new ArrayList<>();
        List<PdcProductMapping> updateList = new ArrayList<>();

        for (GoodsImageSearchResponse.GoodsInfo goodsInfo : goodsInfoList) {
            String platformProductId = String.valueOf(goodsInfo.getOfferId());
            PdcProductMapping existingRecord = existingDataMap.get(platformProductId);

            PdcProductMapping productMapping;
            if (existingRecord != null) {
                // 更新现有记录
                productMapping = pdcProductSearchConvertMapping.createPdcProductMappingFromImageSearchGoodsInfo(goodsInfo, existingRecord.getId());
                // 保留重要的统计信息
                productMapping.setAccessCount(existingRecord.getAccessCount());
                productMapping.setSearchCount(existingRecord.getSearchCount());
                productMapping.setDetailViewCount(existingRecord.getDetailViewCount());
                productMapping.setInteractionCount(existingRecord.getInteractionCount());
                productMapping.setFirstAccessTime(existingRecord.getFirstAccessTime());
                updateList.add(productMapping);
            } else {
                // 新增记录
                productMapping = pdcProductSearchConvertMapping.createPdcProductMappingFromImageSearchGoodsInfo(goodsInfo, null);
                insertList.add(productMapping);
            }
        }

        // 4. 批量执行数据库操作
        if (CollectionUtil.isNotEmpty(insertList)) {
            this.baseMapper.batchInsertProducts(insertList);
            log.debug("批量插入图片搜索商品数据，预期: {} 条，实际: {} 条", insertList.size(), insertList.size());
        }

        if (CollectionUtil.isNotEmpty(updateList)) {
            this.baseMapper.batchUpdateProducts(updateList);
            log.debug("批量更新图片搜索商品数据，预期: {} 条，实际: {} 条", updateList.size(), updateList.size());
        }

        // 合并新增和更新的结果列表
        List<PdcProductMapping> allResults = new ArrayList<>();
        allResults.addAll(insertList);
        allResults.addAll(updateList);

        log.debug("同步图片搜索结果商品数据完成，总处理: {} 条，返回结果: {} 条", goodsInfoList.size(), allResults.size());

        // 🎯 Phase 1.3: 图片搜索同步后触发自动同步到TzProduct表
        List<String> productIds = allResults.stream().map(mapping -> mapping.getPlatformProductId()).collect(Collectors.toList());
        triggerAutoSyncWithEventType(productIds, ProductDataSyncEvent.SyncEventType.IMAGE_SEARCH_SYNCED, "图片搜索同步后触发");

        return allResults;
    }

    /**
     * 批量同步卖家商品数据 - 优化版本
     */
    @Override
    public int syncSellerProductData(List<GoodsSellerResponse.ProductInfo> productInfoList) {
        if (CollectionUtil.isEmpty(productInfoList)) {
            return 0;
        }

        log.debug("开始批量同步卖家商品数据，数量: {}", productInfoList.size());

        // 1. 提取平台商品ID列表
        List<String> platformProductIds = productInfoList.stream().map(product -> String.valueOf(product.getOfferId())).toList();

        // 2. 一次性查询已存在的商品数据
        Map<String, PdcProductMapping> existingDataMap = getExistingProductMappingsOptimized(platformProductIds);

        // 3. 分离新增和更新数据
        List<PdcProductMapping> insertList = new ArrayList<>();
        List<PdcProductMapping> updateList = new ArrayList<>();

        for (GoodsSellerResponse.ProductInfo productInfo : productInfoList) {
            String platformProductId = String.valueOf(productInfo.getOfferId());
            PdcProductMapping existingRecord = existingDataMap.get(platformProductId);

            PdcProductMapping productMapping;
            if (existingRecord != null) {
                // 更新现有记录
                productMapping = pdcProductConvertMapping.createPdcProductMappingFromGoodsInfoBySellerProducts(productInfo, existingRecord.getId());
                // 保留重要的统计信息
                productMapping.setAccessCount(existingRecord.getAccessCount());
                productMapping.setSearchCount(existingRecord.getSearchCount());
                productMapping.setDetailViewCount(existingRecord.getDetailViewCount());
                productMapping.setInteractionCount(existingRecord.getInteractionCount());
                productMapping.setFirstAccessTime(existingRecord.getFirstAccessTime());
                updateList.add(productMapping);
            } else {
                // 新增记录
                productMapping = pdcProductConvertMapping.createPdcProductMappingFromGoodsInfoBySellerProducts(productInfo, null);
                insertList.add(productMapping);
            }
        }

        // 4. 批量执行数据库操作
        int affectedRows = 0;

        if (CollectionUtil.isNotEmpty(insertList)) {
            int insertCount = this.baseMapper.batchInsertProducts(insertList);
            affectedRows += insertCount;
            log.debug("批量插入卖家商品数据，预期: {} 条，实际: {} 条", insertList.size(), insertCount);
        }

        if (CollectionUtil.isNotEmpty(updateList)) {
            int updateCount = this.baseMapper.batchUpdateProducts(updateList);
            affectedRows += updateCount;
            log.debug("批量更新卖家商品数据，预期: {} 条，实际: {} 条", updateList.size(), updateCount);
        }

        log.debug("同步卖家商品数据完成，总处理: {} 条，实际影响: {} 条", productInfoList.size(), affectedRows);

        // 🎯 Phase 1.3: 卖家商品同步后触发自动同步到TzProduct表
        triggerAutoSyncWithEventType(platformProductIds, ProductDataSyncEvent.SyncEventType.SELLER_PRODUCTS_SYNCED, "卖家商品同步后触发");

        return affectedRows;
    }

    /**
     * 搜索商品信息列表 并自动同步入库 - 缓存版本
     *
     * <pre>
     * 缓存策略优化：
     * 1. 分层缓存：完整结果缓存 + 无分页缓存
     * 2. 智能失效：基于数据更新时间和搜索频次
     * 3. 预热策略：热门搜索条件预加载
     * 4. 降级保护：缓存异常时自动降级到直接搜索
     * </pre>
     *
     * @param request        搜索请求参数
     * @param isForceRefresh 是否强制刷新缓存
     * @return 商品信息列表
     */
    @Override
    public PageDTO<ProductInfoDTO> searchProductInfoListSyncWithCache(ProductSearchRequestDTO request, Boolean isForceRefresh) {
        // 1. 强制刷新时直接调用搜索
        if (Objects.nonNull(isForceRefresh) && isForceRefresh) {
            log.debug("强制刷新缓存，直接搜索");
            return searchProductInfoListSync(request);
        }

        try {
            // 2. 尝试从分层缓存获取数据
            return getFromLayeredCache(request);

        } catch (Exception e) {
            log.warn("缓存操作失败，降级到直接搜索, error: {}", e.getMessage(), e);
            return searchProductInfoListSync(request);
        }
    }

    /**
     * 分层缓存获取策略
     */
    private PageDTO<ProductInfoDTO> getFromLayeredCache(ProductSearchRequestDTO request) {
        // Level 1: 完整缓存（包含分页）
        String fullCacheKey = request.cacheKey();
        PageDTO<ProductInfoDTO> cachedResult = productSearchCache.get(fullCacheKey);

        if (Objects.nonNull(cachedResult)) {
            log.debug("命中完整缓存, key: {}", fullCacheKey);
            updateCacheHitStatistics(fullCacheKey, true);
            return cachedResult;
        }

        // Level 2: 无分页缓存（相同搜索条件，不同分页）
        String noPaginationKey = request.cacheKeyWithoutPagination();
        PageDTO<ProductInfoDTO> baseResult = productSearchCache.get(noPaginationKey);

        if (Objects.nonNull(baseResult) && canUsePartialCache(request, baseResult)) {
            log.debug("命中无分页缓存，进行分页处理, key: {}", noPaginationKey);
            PageDTO<ProductInfoDTO> paginatedResult = applyPaginationToCache(baseResult, request);
            // 将分页结果也缓存起来
            productSearchCache.put(fullCacheKey, paginatedResult);
            updateCacheHitStatistics(noPaginationKey, true);
            return paginatedResult;
        }

        // Level 3: 缓存未命中，执行搜索并更新缓存
        log.debug("缓存未命中，执行搜索, fullKey: {}, baseKey: {}", fullCacheKey, noPaginationKey);
        PageDTO<ProductInfoDTO> searchResult = searchProductInfoListSync(request);

        // 更新多层缓存
        updateMultiLevelCache(searchResult, request, fullCacheKey, noPaginationKey);
        updateCacheHitStatistics(fullCacheKey, false);

        return searchResult;
    }

    /**
     * 判断是否可以使用部分缓存
     */
    private boolean canUsePartialCache(ProductSearchRequestDTO request, PageDTO<ProductInfoDTO> baseResult) {
        if (baseResult == null || baseResult.getRecords() == null) {
            return false;
        }

        int requestPage = request.getPage() != null ? request.getPage() : 1;
        int requestPageSize = request.getPageSize() != null ? request.getPageSize() : 20;

        // 检查是否有足够的数据进行分页
        int totalRecords = baseResult.getRecords().size();
        int startIndex = (requestPage - 1) * requestPageSize;

        return startIndex < totalRecords;
    }

    /**
     * 对缓存结果应用分页
     */
    private PageDTO<ProductInfoDTO> applyPaginationToCache(PageDTO<ProductInfoDTO> baseResult, ProductSearchRequestDTO request) {
        int page = request.getPage() != null ? request.getPage() : 1;
        int pageSize = request.getPageSize() != null ? request.getPageSize() : 20;

        List<ProductInfoDTO> allRecords = baseResult.getRecords();
        int startIndex = (page - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, allRecords.size());

        List<ProductInfoDTO> paginatedRecords = allRecords.subList(startIndex, endIndex);

        return PageDTO.<ProductInfoDTO>builder().records(paginatedRecords).total(baseResult.getTotal()).pageIndex(page).pageSize(pageSize).build();
    }

    /**
     * 更新多层缓存
     *
     * <pre>
     * 缓存策略优化：
     * 1. 分层缓存：完整结果缓存 + 无分页缓存
     * 2. 智能失效：基于数据更新时间和搜索频次
     * 3. 预热策略：热门搜索条件预加载
     * 4. 降级保护：缓存异常时自动降级到直接搜索
     * </pre>
     */
    private void updateMultiLevelCache(PageDTO<ProductInfoDTO> searchResult, ProductSearchRequestDTO request, String fullCacheKey, String noPaginationKey) {
        try {
            // 1. 缓存完整结果
            productSearchCache.put(fullCacheKey, searchResult);

            // 2. 如果是第一页且有足够数据，缓存无分页版本
            int page = request.getPage() != null ? request.getPage() : 1;
            if (page == 1 && searchResult.getRecords() != null && !searchResult.getRecords().isEmpty()) {
                // 构建包含更多数据的基础缓存（可以考虑获取更多页的数据）
                productSearchCache.put(noPaginationKey, searchResult);
            }

            log.debug("更新多层缓存完成, fullKey: {}, baseKey: {}", fullCacheKey, noPaginationKey);

        } catch (Exception e) {
            log.warn("更新缓存失败, error: {}", e.getMessage());
        }
    }

    /**
     * 更新缓存命中统计（简化版，可以后续扩展到更完善的监控系统）
     *
     * <pre>
     * TODO: 后续可以扩展到更完善的监控系统
     * 缓存策略优化：
     * 1. 分层缓存：完整结果缓存 + 无分页缓存
     * 2. 智能失效：基于数据更新时间和搜索频次
     * 3. 预热策略：热门搜索条件预加载
     * 4. 降级保护：缓存异常时自动降级到直接搜索
     * </pre>
     *
     * @param cacheKey 缓存key
     * @param isHit    是否命中
     */
    private void updateCacheHitStatistics(String cacheKey, boolean isHit) {
        // 这里可以集成到监控系统，记录缓存命中率
        // 示例：发送到监控系统或记录到日志
        if (log.isDebugEnabled()) {
            log.debug("缓存统计 - Key: {}, Hit: {}", cacheKey, isHit);
        }
    }

    @Override
    public PageDTO<ProductInfoDTO> searchProductInfoListSync(ProductSearchRequestDTO request) {
        log.debug("开始同步搜索商品数据，请求参数: {}", request);

        try {
            // 1. 先从API获取搜索结果
            GoodsSearchResponse.SearchResult searchResult = productManager.searchProducts(request);
            if (searchResult == null || CollectionUtil.isEmpty(searchResult.getData())) {
                log.debug("搜索结果为空");
                return buildEmptyPageResult(request.getPage(), request.getPageSize());
            }

            // 2. 批量同步搜索结果到数据库
            List<PdcProductMapping> pdcProductMappings = syncSearchResultData(searchResult.getData());

            // 3. 构建搜索结果
            return buildSearchResult(pdcProductMappings, searchResult.getTotalRecords(), searchResult.getCurrentPage(), searchResult.getPageSize());

        } catch (Exception e) {
            if (e instanceof BusinessExceptionI18n) {
                throw e;
            }
            log.error("同步搜索商品数据失败", e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.SYSTEM_ERROR, e.getMessage());
        }
    }

    @Override
    public PageDTO<ProductInfoDTO> searchSimilarProductsSync(Long offerId, LanguageEnum language, Integer pageIndex, Integer pageSize) {
        log.debug("开始搜索相似商品并同步，offerId: {}", offerId);

        try {
            // 1. 调用ProductManager获取相似商品
            List<GoodsRelatedRecommendResponse.ProductInfo> relatedProducts = productManager.getRelatedRecommend(offerId, language, pageIndex, pageSize);

            if (CollectionUtil.isEmpty(relatedProducts)) {
                log.debug("相似商品搜索结果为空");
                return buildEmptyPageResult(pageIndex, pageSize);
            }

            // 2. 转换为ProductInfoDTO
            List<ProductInfoDTO> productInfoDTOList = relatedProducts.stream().map(pdcProductRecommendConvertMapping::convertRelatedRecommendProductToProductInfoDTO)
                .collect(Collectors.toList());

            // 3. 批量同步数据到数据库
            syncProductInfoDTOListToDatabase(productInfoDTOList, "相似商品");

            // 4. 构建分页结果
            return PageDTO.<ProductInfoDTO>builder().records(productInfoDTOList).total((long) relatedProducts.size()).pageIndex(pageIndex != null ? pageIndex : 1)
                .pageSize(pageSize != null ? pageSize : 20)
                .build();

        } catch (Exception e) {
            log.error("搜索相似商品失败，offerId: {}", offerId, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.SYSTEM_ERROR, e.getMessage());
        }
    }

    @Override
    public PageDTO<ProductInfoDTO> recommendProducts(LanguageEnum language, Integer pageIndex, Integer pageSize) {
        log.debug("开始获取推荐商品数据，语言: {}, 页码: {}, 每页大小: {}", language, pageIndex, pageSize);

        try {
            // 使用新的统一缓存+同步方法
            return recommendProductsWithCache(language, pageIndex, pageSize, false);

        } catch (Exception e) {
            log.error("获取推荐商品失败，语言: {}", language, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.SYSTEM_ERROR, e.getMessage());
        }
    }

    /**
     * 推荐商品 - 带缓存和同步优化
     *
     * <pre>
     * 统一处理流程：
     * 1. 智能缓存策略 - 基于语言和分页参数生成缓存Key
     * 2. API数据获取 - 调用1688推荐商品API
     * 3. 数据同步 - 统一的ID处理和数据库同步逻辑
     * 4. 结果构建 - 从数据库读取，确保ID一致性
     *
     * 优化特性：
     * - 支持强制刷新缓存
     * - 分层缓存存储 (本地 + 远程)
     * - 异常降级处理
     * - 与搜索商品统一的同步逻辑
     * </pre>
     */
    @Override
    public PageDTO<ProductInfoDTO> recommendProductsWithCache(LanguageEnum language, Integer pageIndex, Integer pageSize, Boolean isForceRefresh) {
        log.debug("推荐商品缓存获取开始，语言: {}, 页码: {}, 强制刷新: {}", language, pageIndex, isForceRefresh);

        // 1. 强制刷新时直接调用同步方法
        if (Objects.nonNull(isForceRefresh) && isForceRefresh) {
            log.debug("强制刷新缓存，直接执行推荐商品同步");
            return recommendProductsSync(language, pageIndex, pageSize);
        }

        try {
            // 2. 尝试从缓存获取数据
            return getFromRecommendCache(language, pageIndex, pageSize);

        } catch (Exception e) {
            log.warn("推荐商品缓存操作失败，降级到直接同步, error: {}", e.getMessage());
            return recommendProductsSync(language, pageIndex, pageSize);
        }
    }

    /**
     * 推荐商品缓存获取策略
     */
    private PageDTO<ProductInfoDTO> getFromRecommendCache(LanguageEnum language, Integer pageIndex, Integer pageSize) {
        // 1. 生成缓存Key
        String cacheKey = buildRecommendCacheKey(language, pageIndex, pageSize);
        PageDTO<ProductInfoDTO> cachedResult = productSearchCache.get(cacheKey);

        if (Objects.nonNull(cachedResult)) {
            log.debug("命中推荐商品缓存, key: {}", cacheKey);
            updateCacheHitStatistics(cacheKey, true);
            return cachedResult;
        }

        // 2. 缓存未命中，执行同步
        log.debug("推荐商品缓存未命中，执行同步, key: {}", cacheKey);
        PageDTO<ProductInfoDTO> syncResult = recommendProductsSync(language, pageIndex, pageSize);

        // 3. 更新缓存
        try {
            productSearchCache.put(cacheKey, syncResult);
            log.debug("推荐商品结果已缓存, key: {}", cacheKey);
        } catch (Exception e) {
            log.warn("更新推荐商品缓存失败, key: {}, error: {}", cacheKey, e.getMessage());
        }

        updateCacheHitStatistics(cacheKey, false);
        return syncResult;
    }

    /**
     * 构建推荐商品缓存Key
     */
    private String buildRecommendCacheKey(LanguageEnum language, Integer pageIndex, Integer pageSize) {
        return String.format("recommend:%s:%d:%d", language != null ? language.name() : "EN", pageIndex != null ? pageIndex : 1, pageSize != null ? pageSize : 20);
    }

    /**
     * 推荐商品同步方法 - 参考搜索商品的成功模式
     */
    private PageDTO<ProductInfoDTO> recommendProductsSync(LanguageEnum language, Integer pageIndex, Integer pageSize) {
        log.debug("开始同步推荐商品数据，语言: {}, 页码: {}, 每页大小: {}", language, pageIndex, pageSize);

        try {
            // 1. 调用ProductManager获取推荐商品API数据
            List<GoodsRecommendResponse.ProductInfo> recommendProducts = productManager.getRecommendProducts(language, pageIndex, pageSize);

            if (CollectionUtil.isEmpty(recommendProducts)) {
                log.debug("推荐商品API数据为空");
                return buildEmptyPageResult(pageIndex, pageSize);
            }

            // 2. 将API数据转换为统一的同步格式，并批量同步到数据库
            List<PdcProductMapping> syncedMappings = syncRecommendProductsToDatabase(recommendProducts);

            // 3. 从数据库读取数据构建结果（确保ID一致性）
            return buildSearchResult(syncedMappings, recommendProducts.size(), pageIndex != null ? pageIndex : 1, pageSize != null ? pageSize : 20);

        } catch (Exception e) {
            if (e instanceof BusinessExceptionI18n) {
                throw e;
            }
            log.error("同步推荐商品数据失败，语言: {}", language, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.SYSTEM_ERROR, e.getMessage());
        }
    }

    /**
     * 推荐商品数据同步到数据库 - 使用统一的同步逻辑
     *
     * <pre>
     * 关键改进：
     * 1. 正确处理ID映射 - 保留1688原始ID作为platformProductId
     * 2. 生成内部ID用于数据库存储和返回
     * 3. 复用现有的批量同步逻辑
     * 4. 保持与搜索商品一致的处理流程
     * </pre>
     */
    private List<PdcProductMapping> syncRecommendProductsToDatabase(List<GoodsRecommendResponse.ProductInfo> recommendProducts) {
        if (CollectionUtil.isEmpty(recommendProducts)) {
            return Collections.emptyList();
        }

        log.debug("开始同步推荐商品到数据库，数量: {}", recommendProducts.size());

        // 1. 提取平台商品ID并查询已存在的数据
        List<String> platformProductIds = recommendProducts.stream().map(product -> String.valueOf(product.getOfferId())).toList();

        Map<String, PdcProductMapping> existingDataMap = getExistingProductMappingsOptimized(platformProductIds);

        // 2. 处理每个推荐商品，分离新增和更新
        List<PdcProductMapping> insertList = new ArrayList<>();
        List<PdcProductMapping> updateList = new ArrayList<>();
        List<PdcProductMapping> resultList = new ArrayList<>();

        for (GoodsRecommendResponse.ProductInfo productInfo : recommendProducts) {
            String platformProductId = String.valueOf(productInfo.getOfferId());
            PdcProductMapping existingRecord = existingDataMap.get(platformProductId);

            // 创建/更新ProductMapping
            PdcProductMapping productMapping = createPdcProductMappingFromRecommendProduct(productInfo, existingRecord);

            if (existingRecord != null) {
                // 更新现有记录：使用原ID并保留统计信息
                productMapping.setId(existingRecord.getId());
                preserveStatisticsData(productMapping, existingRecord);
                updateList.add(productMapping);
            } else {
                // 新增记录
                insertList.add(productMapping);
            }

            resultList.add(productMapping);
        }

        // 3. 批量执行数据库操作
        executeBatchDatabaseOperations(new ProcessResult(insertList, updateList, resultList));

        log.debug("推荐商品同步完成，新增: {} 条，更新: {} 条", insertList.size(), updateList.size());

        // 🎯 Phase 1.3: 推荐商品同步后触发自动同步到TzProduct表
        List<String> productIds = resultList.stream().map(PdcProductMapping::getPlatformProductId).collect(Collectors.toList());
        triggerAutoSyncWithEventType(productIds, ProductDataSyncEvent.SyncEventType.RECOMMEND_PRODUCTS_SYNCED, "推荐商品同步后触发");

        return resultList;
    }

    /**
     * 从推荐商品API数据创建PdcProductMapping
     *
     * <pre>
     * 关键逻辑：
     * 1. 生成内部ID用于数据库存储
     * 2. 保存1688原始ID作为platformProductId
     * 3. 转换价格和基本信息
     * 4. 标记为未同步状态，等待详情同步
     * </pre>
     */
    private PdcProductMapping createPdcProductMappingFromRecommendProduct(GoodsRecommendResponse.ProductInfo productInfo, PdcProductMapping existingRecord) {
        // 如果有已存在记录则使用其ID，否则生成新ID
        long id;
        if (existingRecord != null) {
            id = existingRecord.getId();
        } else {
            IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");
            id = idGenerator.generate();
        }

        // 保存原始的平台商品ID
        String originalOfferId = String.valueOf(productInfo.getOfferId());

        PdcProductMapping.PdcProductMappingBuilder builder = PdcProductMapping.builder().id(id).platformCode(PlatformCodeEnum.PLATFORM_CODE_1688).platformProductId(originalOfferId)
            .platformProductName(productInfo.getSubject()).platformProductNameTrans(productInfo.getSubjectTrans()).platformProductMainImage(productInfo.getImageUrl())
            .platformProductSoldOut(productInfo.getMonthSold()).isSynced(PdcProductMappingSyncStatusEnum.NOT_SYNCED);

        // 处理价格信息
        if (productInfo.getPriceInfo() != null) {
            BigDecimal price = extractPriceFromRecommendProduct(productInfo.getPriceInfo());
            if (price != null) {
                builder.platformProductPrice(price);
                // 如果是一件代发价则同时设置代发价字段
                if (productInfo.getPriceInfo().getConsignPrice() != null) {
                    builder.platformProductPriceCosign(price);
                }
            }
        }

        return builder.build();
    }

    /**
     * 从推荐商品价格信息中提取最优价格
     */
    private BigDecimal extractPriceFromRecommendProduct(GoodsRecommendResponse.PriceInfo priceInfo) {
        if (priceInfo == null) {
            return null;
        }

        // 优先使用一件代发价
        if (priceInfo.getConsignPrice() != null) {
            try {
                return new BigDecimal(priceInfo.getConsignPrice());
            } catch (NumberFormatException e) {
                log.warn("推荐商品一件代发价格格式异常: {}", priceInfo.getConsignPrice());
            }
        }

        // 其次使用批发价
        if (priceInfo.getPrice() != null) {
            try {
                return new BigDecimal(priceInfo.getPrice());
            } catch (NumberFormatException e) {
                log.warn("推荐商品价格格式异常: {}", priceInfo.getPrice());
            }
        }

        return null;
    }

    /**
     * 统一聚合搜索商品 - 带缓存优化
     *
     * <pre>
     * 统一处理所有搜索类型的核心逻辑：
     * 1. 关键词搜索、图片搜索、混合搜索统一入口
     * 2. 智能缓存策略 - 基于请求参数生成缓存Key
     * 3. 统一的数据同步和转换逻辑
     * 4. 自动失效和降级策略
     *
     * 优化特性：
     * - 支持强制刷新缓存
     * - 分层缓存存储 (本地 + 远程)
     * - 异常降级处理
     * - 性能监控和统计
     * </pre>
     */
    @Override
    public PageDTO<ProductInfoDTO> unifiedAggregateSearchWithCache(AggregateSearchReq request, Boolean isForceRefresh) {
        log.debug("统一聚合搜索开始，搜索类型: {}, 强制刷新: {}", request.getSearchType(), isForceRefresh);

        // 1. 强制刷新时直接调用搜索
        if (Objects.nonNull(isForceRefresh) && isForceRefresh) {
            log.debug("强制刷新缓存，直接执行搜索");
            return executeUnifiedSearch(request);
        }

        try {
            // 2. 尝试从聚合搜索缓存获取数据
            return getFromAggregateSearchCache(request);

        } catch (Exception e) {
            log.warn("聚合搜索缓存操作失败，降级到直接搜索, error: {}", e.getMessage());
            return executeUnifiedSearch(request);
        }
    }

    /**
     * 聚合搜索缓存获取策略
     *
     * <pre>
     * 缓存Key生成规则：
     * - 包含搜索类型、关键词、图片ID、分页参数等
     * - 自动排除空值参数
     * - 支持参数顺序无关的缓存命中
     * </pre>
     */
    private PageDTO<ProductInfoDTO> getFromAggregateSearchCache(AggregateSearchReq request) {
        // 1. 生成缓存Key
        String cacheKey = request.buildAggregateSearchCacheKey();
        PageDTO<ProductInfoDTO> cachedResult = productSearchCache.get(cacheKey);

        if (Objects.nonNull(cachedResult)) {
            log.debug("命中聚合搜索缓存, key: {}", cacheKey);
            updateCacheHitStatistics(cacheKey, true);
            return cachedResult;
        }

        // 2. 缓存未命中，执行搜索
        log.debug("聚合搜索缓存未命中，执行搜索, key: {}", cacheKey);
        PageDTO<ProductInfoDTO> searchResult = executeUnifiedSearch(request);

        // 3. 更新缓存
        try {
            productSearchCache.put(cacheKey, searchResult);
            log.debug("聚合搜索结果已缓存, key: {}", cacheKey);
        } catch (Exception e) {
            log.warn("更新聚合搜索缓存失败, key: {}, error: {}", cacheKey, e.getMessage());
        }

        updateCacheHitStatistics(cacheKey, false);
        return searchResult;
    }

    /**
     * 执行统一搜索逻辑 - 核心搜索方法
     *
     * <pre>
     * 统一处理所有搜索类型的核心逻辑：
     * 1. 参数验证和预处理
     * 2. 根据搜索类型调用相应的API
     * 3. 统一的数据同步和转换
     * 4. 统一的结果构建和错误处理
     * </pre>
     */
    private PageDTO<ProductInfoDTO> executeUnifiedSearch(AggregateSearchReq request) {
        log.debug("执行统一搜索，搜索类型: {}", request.getSearchType());

        try {
            SearchType searchType = SearchType.fromValue(request.getSearchType());
            // 2. 根据搜索类型执行相应的搜索逻辑
            return switch (searchType) {
                case KEYWORD -> executeKeywordSearch(request);
                case IMAGE -> executeImageSearch(request);
                default -> throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.UNSUPPORTED_SEARCH_TYPE, request.getSearchType());
            };

        } catch (BusinessExceptionI18n e) {
            throw e; // 重新抛出业务异常
        } catch (Exception e) {
            log.error("统一搜索执行失败，搜索类型: {}, 错误: {}", request.getSearchType(), e.getMessage(), e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.SYSTEM_ERROR, e.getMessage());
        }
    }

    /**
     * 执行关键词搜索
     */
    private PageDTO<ProductInfoDTO> executeKeywordSearch(AggregateSearchReq request) {
        log.debug("执行关键词搜索，关键词: {}", request.getKeyword());

        // 构建ProductSearchRequestDTO
        ProductSearchRequestDTO searchRequest = buildProductSearchRequest(request);

        // 调用关键词搜索方法
        return searchProductInfoListSync(searchRequest);
    }

    /**
     * 执行图片搜索
     *
     * <pre>
     * 优化的图片搜索逻辑：
     * 1. 支持imageId和imageUrl两种搜索方式
     * 2. 优先使用imageId，如果没有则使用imageUrl
     * 3. 支持混合搜索（图片+关键词）
     * 4. 完整的字段映射和参数验证
     * </pre>
     */
    private PageDTO<ProductInfoDTO> executeImageSearch(AggregateSearchReq request) {
        log.debug("执行图片搜索，图片ID: {}, 图片URL: {}, 关键词: {}", request.getImageId(), request.getImageUrl(), request.getKeyword());

        try {
            // 1. 构建图片搜索请求 - 使用完整的参数映射
            GoodsImageSearchResponse.SearchResult searchResult = productManager.searchProductsByImage(request);

            // 4. 检查搜索结果
            if (searchResult == null || CollectionUtil.isEmpty(searchResult.getData())) {
                log.debug("图片搜索结果为空，图片ID: {}, 图片URL: {}", request.getImageId(), request.getImageUrl());
                return buildEmptyPageResult(request);
            }

            // 5. 批量同步图片搜索结果到数据库
            List<PdcProductMapping> syncedMappings = syncImageSearchResultData(searchResult.getData());

            // 6. 构建返回结果
            PageDTO<ProductInfoDTO> result = buildSearchResult(syncedMappings, searchResult.getTotalRecords(), searchResult.getCurrentPage(), searchResult.getPageSize());

            log.debug("图片搜索完成，返回结果数量: {}", result.getRecords().size());
            return result;

        } catch (BusinessExceptionI18n e) {
            throw e;
        } catch (Exception e) {
            log.error("图片搜索执行失败，图片ID: {}, 图片URL: {}", request.getImageId(), request.getImageUrl(), e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.SYSTEM_ERROR, e.getMessage());
        }
    }

    /**
     * 构建ProductSearchRequestDTO
     */
    private ProductSearchRequestDTO buildProductSearchRequest(AggregateSearchReq request) {
        ProductSearchRequestDTO searchRequest = ProductSearchRequestDTO.builder().keyword(request.getKeyword()).language(LanguageEnum.EN).page(request.getPage())
            .pageSize(request.getPageSize())
            .categoryId(request.getCategoryId()).minPrice(request.getMinPrice()).maxPrice(request.getMaxPrice()).sortField(request.getSortField()).sortOrder(request.getSortOrder())
            .filter(request.getFilter()).snId(request.getSnId()).keywordTranslate(request.getKeywordTranslate()).build();

        // 处理分类ID列表
        if (request.getCategoryIdList() != null && !request.getCategoryIdList().isEmpty()) {
            String categoryIdListStr = request.getCategoryIdList().stream().map(String::valueOf).collect(Collectors.joining(","));
            searchRequest.setCategoryIdList(categoryIdListStr);
        }

        return searchRequest;
    }

    /**
     * 构建空的分页结果
     */
    private PageDTO<ProductInfoDTO> buildEmptyPageResult(AggregateSearchReq request) {
        return buildEmptyPageResult(request.getPage(), request.getPageSize());
    }

    /**
     * 构建空的分页结果 - 通用方法
     */
    private PageDTO<ProductInfoDTO> buildEmptyPageResult(Integer page, Integer pageSize) {
        return PageDTO.<ProductInfoDTO>builder().records(Collections.emptyList()).total(0L).pageIndex(page != null ? page : 1).pageSize(pageSize != null ? pageSize : 20).build();
    }

    /**
     * 构建搜索结果
     */
    private PageDTO<ProductInfoDTO> buildSearchResult(List<PdcProductMapping> mappings, int totalRecords, int currentPage, int pageSize) {
        List<ProductInfoDTO> resultList = mappings.stream().map(pdcProductConvertMapping::convertPdcProductMappingToProductInfoDTO).collect(Collectors.toList());

        return PageDTO.<ProductInfoDTO>builder().records(resultList).total((long) totalRecords).pageIndex(currentPage).pageSize(pageSize).build();
    }

    /**
     * 通用的ProductInfoDTO列表同步到数据库方法
     *
     * @param productInfoDTOList 商品信息DTO列表
     * @param dataType           数据类型描述（用于日志）
     * @return 影响的行数
     */
    private int syncProductInfoDTOListToDatabase(List<ProductInfoDTO> productInfoDTOList, String dataType) {
        if (CollectionUtil.isEmpty(productInfoDTOList)) {
            return 0;
        }

        // 1. 提取平台商品ID并查询已存在的数据
        List<String> platformProductIds = productInfoDTOList.stream().map(dto -> String.valueOf(dto.getId())).toList();

        Map<String, PdcProductMapping> existingDataMap = getExistingProductMappingsOptimized(platformProductIds);

        // 2. 分离新增和更新数据
        List<PdcProductMapping> insertList = new ArrayList<>();
        List<PdcProductMapping> updateList = new ArrayList<>();

        for (ProductInfoDTO productInfoDTO : productInfoDTOList) {
            String platformProductId = String.valueOf(productInfoDTO.getId());
            PdcProductMapping existingRecord = existingDataMap.get(platformProductId);

            PdcProductMapping productMapping = pdcProductConvertMapping.createPdcProductMappingFromProductInfoDTO(productInfoDTO);

            if (existingRecord != null) {
                // 更新现有记录：使用原ID并保留统计信息
                productMapping.setId(existingRecord.getId());
                preserveStatisticsData(productMapping, existingRecord);
                updateList.add(productMapping);
            } else {
                // 新增记录
                insertList.add(productMapping);
            }
        }

        // 3. 批量执行数据库操作
        int affectedRows = 0;

        if (CollectionUtil.isNotEmpty(insertList)) {
            int insertCount = this.baseMapper.batchInsertProducts(insertList);
            affectedRows += insertCount;
            log.debug("批量插入{}数据，预期: {} 条，实际: {} 条", dataType, insertList.size(), insertCount);
        }

        if (CollectionUtil.isNotEmpty(updateList)) {
            int updateCount = this.baseMapper.batchUpdateProducts(updateList);
            affectedRows += updateCount;
            log.debug("批量更新{}数据，预期: {} 条，实际: {} 条", dataType, updateList.size(), updateCount);
        }

        log.debug("{}同步完成，总处理: {} 条，实际影响: {} 条", dataType, productInfoDTOList.size(), affectedRows);

        // 🎯 Phase 1.3: 通用同步后触发自动同步到TzProduct表
        triggerAutoSyncWithEventType(platformProductIds, ProductDataSyncEvent.SyncEventType.SIMILAR_PRODUCTS_SYNCED, "相似商品同步后触发");

        return affectedRows;
    }

    /**
     * 清理聚合搜索缓存
     *
     * <pre>
     * 使用场景：
     * 1. 商品数据更新后需要清理相关缓存
     * 2. 系统维护时批量清理缓存
     * 3. 缓存策略调整时重置缓存
     * </pre>
     *
     * @param searchType 搜索类型，null表示清理所有类型
     * @param keyword    关键词，null表示清理所有关键词
     */
    public void clearAggregateSearchCache(Integer searchType, String keyword) {
        try {
            // 这里应该实现缓存清理逻辑
            // 由于当前缓存框架的限制，我们只能记录日志
            log.info("请求清理聚合搜索缓存 - 搜索类型: {}, 关键词: {}", searchType, keyword);

            // TODO: 实现精确的缓存清理逻辑
            // 可以考虑使用缓存标签或模式匹配来实现批量清理

        } catch (Exception e) {
            log.error("清理聚合搜索缓存失败", e);
        }
    }

    /**
     * 预热聚合搜索缓存
     *
     * <pre>
     * 预热策略：
     * 1. 热门关键词预加载
     * 2. 常用分页组合预加载
     * 3. 新商品上架后相关搜索预加载
     * </pre>
     *
     * @param popularKeywords 热门关键词列表
     */
    public void warmupAggregateSearchCache(List<String> popularKeywords) {
        if (CollectionUtil.isEmpty(popularKeywords)) {
            return;
        }

        log.info("开始预热聚合搜索缓存，关键词数量: {}", popularKeywords.size());

        // 异步预热，避免阻塞主线程
        CompletableFuture.runAsync(() -> {
            for (String keyword : popularKeywords) {
                try {
                    // 预热关键词搜索
                    AggregateSearchReq warmupRequest = AggregateSearchReq.builder().searchType(AggregateSearchReq.SearchType.KEYWORD.getValue()) // 关键词搜索
                        .keyword(keyword).page(1).pageSize(20).build();

                    unifiedAggregateSearchWithCache(warmupRequest, false);

                    // 避免请求过于频繁
                    Thread.sleep(100);

                } catch (Exception e) {
                    log.warn("预热关键词缓存失败: {}", keyword, e);
                }
            }
            log.info("聚合搜索缓存预热完成");
        }, threadPoolTaskExecutor);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 初始化自动同步配置
        this.autoSyncEnabled = productSyncConfig.isAutoSyncEnabled();
        log.info("🎯 自动同步功能初始化: {}", autoSyncEnabled ? "启用" : "禁用");

        // 初始化缓存配置
        // 商品详情缓存
        QuickConfig quickConfig = QuickConfig.newBuilder("pdc:product:")
            // 本地缓存时间
            .localExpire(Duration.ofMinutes(15))
            // 远程缓存时间
            .expire(Duration.ofMinutes(20))
            // 开启缓存空值
            .cacheNullValue(true)
            // two level cache
            .cacheType(CacheType.BOTH)
            // 本地缓存数量 300
            .localLimit(300)
            // 更新后在所有JVM进程中失效本地缓存
            .syncLocal(true).build();
        productDetailCache = cacheManager.getOrCreateCache(quickConfig);

        // 商品搜索结果缓存
        QuickConfig productSearchCacheConfig = QuickConfig.newBuilder("pdc:product:search:")
            // 本地缓存时间
            .localExpire(Duration.ofMinutes(5))
            // 远程缓存时间
            .expire(Duration.ofMinutes(10))
            // 开启缓存空值
            .cacheNullValue(true)
            // two level cache
            .cacheType(CacheType.BOTH)
            // 本地缓存数量 200
            .localLimit(200)
            // 更新后在所有JVM进程中失效本地缓存
            .syncLocal(true).build();
        productSearchCache = cacheManager.getOrCreateCache(productSearchCacheConfig);
    }

    /**
     * 🎯 事件驱动的自动同步触发机制
     *
     * <p>
     * 优雅解决循环依赖：通过发布事件替代直接调用ProductSyncService 当PdcProductMapping数据发生变化时，发布ProductDataSyncEvent事件 ProductSyncEventListener异步监听并处理同步逻辑
     * </p>
     *
     * @param productIds 需要同步的产品ID列表
     */
    private void triggerAutoSync(List<String> productIds) {
        triggerAutoSyncWithEventType(productIds, ProductDataSyncEvent.SyncEventType.SEARCH_RESULT_SYNCED, "数据同步触发");
    }

    /**
     * 带事件类型的自动同步触发
     *
     * @param productIds  需要同步的产品ID列表
     * @param eventType   事件类型
     * @param description 事件描述
     */
    private void triggerAutoSyncWithEventType(List<String> productIds, ProductDataSyncEvent.SyncEventType eventType, String description) {
        if (!autoSyncEnabled || CollectionUtil.isEmpty(productIds)) {
            if (!autoSyncEnabled) {
                log.debug("自动同步功能已关闭，跳过事件发布");
            }
            return;
        }

        try {
            // 🎯 发布产品同步事件，解决循环依赖
            ProductDataSyncEvent event = new ProductDataSyncEvent(this, productIds, eventType, description);

            eventPublisher.publishEvent(event);

            log.debug("📤 已发布产品同步事件: {} - 产品数量: {}", eventType.getDescription(), productIds.size());

        } catch (Exception e) {
            log.warn("⚠️ 发布产品同步事件失败: 产品数量: {}, 错误: {}", productIds.size(), e.getMessage(), e);
        }
    }

    // ==================== 🎯 Phase 1.3: 自动同步触发机制 ====================

    /**
     * 获取自动同步功能状态
     *
     * @return 自动同步是否启用
     */
    public boolean isAutoSyncEnabled() {
        return autoSyncEnabled;
    }

    /**
     * 设置自动同步功能开关
     *
     * @param enabled 是否启用自动同步
     */
    public void setAutoSyncEnabled(boolean enabled) {
        this.autoSyncEnabled = enabled;
        log.info("自动同步功能设置为: {}", enabled ? "启用" : "禁用");
    }

    /**
     * 产品映射任务内部类
     */
    private static class ProductMappingTask {

        private final PdcProductMapping productMapping;
        private final boolean isUpdate;

        public ProductMappingTask(PdcProductMapping productMapping, boolean isUpdate) {
            this.productMapping = productMapping;
            this.isUpdate = isUpdate;
        }

        public PdcProductMapping getProductMapping() {
            return productMapping;
        }

        public boolean isUpdate() {
            return isUpdate;
        }
    }

    /**
     * 处理结果内部类
     */
    private static class ProcessResult {

        private final List<PdcProductMapping> insertList;
        private final List<PdcProductMapping> updateList;
        private final List<PdcProductMapping> resultList;

        public ProcessResult(List<PdcProductMapping> insertList, List<PdcProductMapping> updateList, List<PdcProductMapping> resultList) {
            this.insertList = insertList;
            this.updateList = updateList;
            this.resultList = resultList;
        }
    }

}
