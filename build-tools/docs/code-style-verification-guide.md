# FulfillmenJavaStyle.xml 验证指南

本文档提供详细的步骤来验证 `FulfillmenJavaStyle.xml` 代码格式化配置文件是否正确生效。

## 1. IDE 配置验证

### 1.1 Eclipse IDE 配置

#### 导入格式化配置

1. 打开 Eclipse IDE
2. 进入 `Window` → `Preferences` (macOS: `Eclipse` → `Preferences`)
3. 导航到 `Java` → `Code Style` → `Formatter`
4. 点击 `Import...` 按钮
5. 选择 `build-tools/src/main/resources/config/FulfillmenJavaStyle.xml` 文件
6. 确认配置名称为 `FulfillmenGoogleStyle`
7. 点击 `Apply and Close`

#### 验证配置加载

1. 在 `Formatter` 页面，确认 `Active profile` 显示为 `FulfillmenGoogleStyle`
2. 点击 `Edit...` 按钮查看详细配置：
    - `Line Wrapping` → `Maximum line width`: 应显示 `210`
    - `Indentation` → `Tab policy`: 应显示 `Spaces only`
    - `Indentation` → `Indentation size`: 应显示 `4`
    - `Indentation` → `Tab size`: 应显示 `4`

#### 应用到项目

1. 右键点击项目 → `Properties`
2. 进入 `Java Code Style` → `Formatter`
3. 选择 `Enable project specific settings`
4. 选择 `FulfillmenGoogleStyle` 配置
5. 点击 `Apply and Close`

### 1.2 IntelliJ IDEA 配置

#### 导入 Eclipse 格式化配置

1. 打开 IntelliJ IDEA
2. 进入 `File` → `Settings` (macOS: `IntelliJ IDEA` → `Preferences`)
3. 导航到 `Editor` → `Code Style` → `Java`
4. 点击齿轮图标 → `Import Scheme` → `Eclipse XML Profile`
5. 选择 `build-tools/src/main/resources/config/FulfillmenJavaStyle.xml` 文件
6. 输入方案名称：`FulfillmenGoogleStyle`
7. 点击 `OK`

#### 验证配置加载

1. 在 `Code Style` → `Java` 页面：
    - `Wrapping and Braces` → `Hard wrap at`: 应显示 `210`
    - `Tabs and Indents` → `Use tab character`: 应为未选中
    - `Tabs and Indents` → `Tab size`: 应显示 `4`
    - `Tabs and Indents` → `Indent`: 应显示 `4`

#### 应用到项目

1. 确保在 `Code Style` 页面选择了 `FulfillmenGoogleStyle` 方案
2. 点击 `Apply` → `OK`

### 1.3 VS Code 配置

#### 安装必要插件

```bash
# 安装Java相关插件
code --install-extension vscjava.vscode-java-pack
code --install-extension redhat.java
```

#### 配置格式化

在项目根目录创建 `.vscode/settings.json`：

```json
{
    "java.format.settings.url": "./build-tools/src/main/resources/config/FulfillmenJavaStyle.xml",
    "java.format.settings.profile": "FulfillmenGoogleStyle",
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.rulers": [210],
    "java.format.enabled": true
}
```

## 2. 格式化效果测试

### 2.1 使用测试文件验证

1. 打开测试文件：`build-tools/src/test/java/com/fulfillmen/shop/test/CodeStyleTestFile.java`
2. 故意破坏格式（添加不规范的空格、缩进、换行）
3. 应用格式化：
    - Eclipse: `Ctrl+Shift+F` (macOS: `Cmd+Shift+F`)
    - IntelliJ IDEA: `Ctrl+Alt+L` (macOS: `Cmd+Option+L`)
    - VS Code: `Shift+Alt+F` (macOS: `Shift+Option+F`)

### 2.2 关键配置验证清单

#### ✅ 缩进验证

-   [ ] 使用 4 个空格缩进，不使用制表符
-   [ ] 类体、方法体、代码块正确缩进
-   [ ] switch 语句的 case 标签正确缩进

#### ✅ 大括号位置验证

-   [ ] 类声明的大括号在行尾：`public class Test {`
-   [ ] 方法声明的大括号在行尾：`public void method() {`
-   [ ] if/for/while 语句的大括号在行尾

#### ✅ 空格验证

-   [ ] 操作符前后有空格：`a + b`, `x == y`
-   [ ] 逗号后有空格：`method(a, b, c)`
-   [ ] 关键字后有空格：`if (condition)`, `for (int i = 0; i < 10; i++)`
-   [ ] 方法调用括号前无空格：`method()`

#### ✅ 换行验证

-   [ ] 长行在 210 字符处换行
-   [ ] 参数列表换行时正确缩进
-   [ ] 链式调用换行时正确缩进

#### ✅ 空行验证

-   [ ] package 声明后有 1 行空行
-   [ ] import 语句后有 1 行空行
-   [ ] 方法之间有 1 行空行
-   [ ] 类体第一个声明前有 1 行空行

### 2.3 自动化验证脚本

创建验证脚本 `build-tools/scripts/verify-code-style.sh`：

```bash
#!/bin/bash

echo "开始验证代码格式化配置..."

# 检查测试文件是否存在
TEST_FILE="build-tools/src/test/java/com/fulfillmen/shop/test/CodeStyleTestFile.java"
if [ ! -f "$TEST_FILE" ]; then
    echo "❌ 测试文件不存在: $TEST_FILE"
    exit 1
fi

# 检查配置文件是否存在
CONFIG_FILE="build-tools/src/main/resources/config/FulfillmenJavaStyle.xml"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 配置文件不存在: $CONFIG_FILE"
    exit 1
fi

# 验证配置文件中的关键设置
echo "验证配置文件关键设置..."

# 检查换行长度设置
if grep -q 'lineSplit.*value="210"' "$CONFIG_FILE"; then
    echo "✅ 代码换行长度设置正确: 210字符"
else
    echo "❌ 代码换行长度设置错误"
fi

# 检查注释换行长度设置
if grep -q 'comment.line_length.*value="210"' "$CONFIG_FILE"; then
    echo "✅ 注释换行长度设置正确: 210字符"
else
    echo "❌ 注释换行长度设置错误"
fi

# 检查缩进设置
if grep -q 'indentation.size.*value="4"' "$CONFIG_FILE"; then
    echo "✅ 缩进大小设置正确: 4个空格"
else
    echo "❌ 缩进大小设置错误"
fi

# 检查制表符设置
if grep -q 'tabulation.char.*value="space"' "$CONFIG_FILE"; then
    echo "✅ 制表符设置正确: 使用空格"
else
    echo "❌ 制表符设置错误"
fi

echo "配置文件验证完成！"
```

## 3. Maven/Gradle 集成

### 3.1 Maven 集成

在 `pom.xml` 中添加格式化插件：

```xml
<plugin>
    <groupId>net.revelc.code.formatter</groupId>
    <artifactId>formatter-maven-plugin</artifactId>
    <version>2.23.0</version>
    <configuration>
        <configFile>${project.basedir}/build-tools/src/main/resources/config/FulfillmenJavaStyle.xml</configFile>
        <lineEnding>LF</lineEnding>
        <encoding>UTF-8</encoding>
    </configuration>
    <executions>
        <execution>
            <goals>
                <goal>validate</goal>
            </goals>
        </execution>
    </executions>
</plugin>
```

验证命令：

```bash
# 检查格式化
mvn formatter:validate

# 应用格式化
mvn formatter:format
```

### 3.2 Gradle 集成

在 `build.gradle` 中添加：

```gradle
plugins {
    id 'com.diffplug.spotless' version '6.22.0'
}

spotless {
    java {
        eclipse().configFile('build-tools/src/main/resources/config/FulfillmenJavaStyle.xml')
        encoding 'UTF-8'
        lineEndings 'UNIX'
    }
}
```

验证命令：

```bash
# 检查格式化
./gradlew spotlessCheck

# 应用格式化
./gradlew spotlessApply
```

## 4. 持续集成验证

### 4.1 GitHub Actions 配置

创建 `.github/workflows/code-style-check.yml`：

```yaml
name: Code Style Check

on:
    pull_request:
        branches: [main, develop]

jobs:
    code-style:
        runs-on: ubuntu-latest

        steps:
            - uses: actions/checkout@v3

            - name: Set up JDK 17
              uses: actions/setup-java@v3
              with:
                  java-version: "17"
                  distribution: "temurin"

            - name: Cache Maven dependencies
              uses: actions/cache@v3
              with:
                  path: ~/.m2
                  key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}

            - name: Verify code formatting
              run: |
                  chmod +x build-tools/scripts/verify-code-style.sh
                  ./build-tools/scripts/verify-code-style.sh
                  mvn formatter:validate
```

### 4.2 GitLab CI 配置

创建 `.gitlab-ci.yml` 片段：

```yaml
code-style-check:
    stage: test
    image: openjdk:17-jdk
    script:
        - chmod +x build-tools/scripts/verify-code-style.sh
        - ./build-tools/scripts/verify-code-style.sh
        - mvn formatter:validate
    only:
        - merge_requests
        - main
        - develop
```

## 5. 常见问题和解决方案

### 5.1 配置不生效

**问题**: IDE 格式化后代码风格没有改变
**解决方案**:

1. 确认配置文件路径正确
2. 重启 IDE
3. 清除 IDE 缓存
4. 检查项目级别的配置是否覆盖了全局配置

### 5.2 换行长度不正确

**问题**: 代码没有在 210 字符处换行
**解决方案**:

1. 检查 IDE 的换行设置
2. 确认配置文件中的 `lineSplit` 值为 `210`
3. 在 IntelliJ IDEA 中检查 `Hard wrap at` 设置

### 5.3 缩进不一致

**问题**: 部分代码使用制表符，部分使用空格
**解决方案**:

1. 全选代码并重新格式化
2. 配置 IDE 显示空白字符
3. 使用 `EditorConfig` 文件统一设置

### 5.4 团队成员配置不一致

**问题**: 不同开发者的格式化结果不同
**解决方案**:

1. 统一使用项目级别的配置
2. 在代码审查中检查格式化
3. 使用 CI/CD 自动检查
4. 定期同步配置文件

## 6. 配置调优建议

### 6.1 根据团队反馈调整

1. 收集团队对当前配置的反馈
2. 识别常见的格式化问题
3. 在配置文件中调整相应参数
4. 重新测试和验证

### 6.2 版本控制最佳实践

1. 将配置文件纳入版本控制
2. 配置变更时通知团队
3. 提供配置变更的说明文档
4. 保持配置文件的向后兼容性

## 7. 快速验证清单

### 7.1 配置文件检查清单

-   [ ] `FulfillmenJavaStyle.xml` 文件存在且可读
-   [ ] 换行长度设置为 210 字符
-   [ ] 缩进设置为 4 个空格
-   [ ] 大括号位置设置为行尾
-   [ ] 包含详细的中文注释说明

### 7.2 IDE 配置检查清单

-   [ ] Eclipse/IntelliJ IDEA 成功导入配置
-   [ ] IDE 显示正确的格式化配置名称
-   [ ] 项目级别配置已启用
-   [ ] 格式化快捷键工作正常

### 7.3 格式化效果检查清单

-   [ ] 测试文件格式化后符合预期
-   [ ] 长行正确在 210 字符处换行
-   [ ] 缩进使用空格而非制表符
-   [ ] 操作符前后空格正确
-   [ ] 空行数量符合配置

### 7.4 团队协作检查清单

-   [ ] 所有团队成员使用相同配置
-   [ ] CI/CD 流程集成格式化检查
-   [ ] 代码审查包含格式化检查
-   [ ] 配置文件纳入版本控制

## 8. 故障排除指南

### 8.1 常见错误及解决方案

| 问题      | 可能原因        | 解决方案          |
|---------|-------------|---------------|
| 格式化不生效  | IDE 未正确加载配置 | 重启 IDE，重新导入配置 |
| 换行长度错误  | IDE 设置覆盖了配置 | 检查 IDE 的换行设置  |
| 缩进不一致   | 混用制表符和空格    | 全选代码重新格式化     |
| 团队格式不统一 | 配置版本不一致     | 同步最新配置文件      |

### 8.2 验证命令速查

```bash
# 运行验证脚本
./build-tools/verify-code-style.sh

# Maven 格式化检查
mvn formatter:validate

# Maven 自动格式化
mvn formatter:format

# Gradle 格式化检查
./gradlew spotlessCheck

# Gradle 自动格式化
./gradlew spotlessApply
```

## 9. 相关文件清单

- **配置文件**: `build-tools/src/main/resources/config/FulfillmenJavaStyle.xml`
- **测试文件**: `build-tools/src/test/java/com/fulfillmen/shop/test/CodeStyleTestFile.java`
- **验证脚本**: `build-tools/verify-code-style.sh`
- **验证指南**: `build-tools/docs/code-style-verification-guide.md`
- **Maven 示例**: `build-tools/maven-formatter-plugin-example.xml`
- **Gradle 示例**: `build-tools/gradle-spotless-example.gradle`
- **GitHub Actions**: `build-tools/github-actions-code-style.yml`
- **EditorConfig**: `.editorconfig`

这个验证指南确保了代码格式化配置的正确性和一致性，帮助团队维护统一的代码风格。
