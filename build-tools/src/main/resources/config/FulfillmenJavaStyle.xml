<?xml version="1.0" encoding="utf-8"?>
<!--
    Fulfillmen Java代码格式化配置文件
    基于Google Java Style Guide，适配团队开发规范
    配置版本：Eclipse JDT 21
-->
<profiles version="21">
  <profile kind="CodeFormatterProfile" name="FulfillmenGoogleStyle" version="21">

    <!-- ========== 缩进和制表符配置 ========== -->
    <!-- 使用空格而非制表符进行缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.tabulation.char" value="space"/>
    <!-- 禁用仅在行首使用制表符的模式 -->
    <setting id="org.eclipse.jdt.core.formatter.use_tabs_only_for_leading_indentations" value="false"/>
    <!-- 缩进大小：4个空格 -->
    <setting id="org.eclipse.jdt.core.formatter.indentation.size" value="4"/>
    <!-- 制表符大小：4个空格 -->
    <setting id="org.eclipse.jdt.core.formatter.tabulation.size" value="4"/>
    <!-- 文本块缩进：0个额外空格 -->
    <setting id="org.eclipse.jdt.core.formatter.text_block_indentation" value="0"/>

    <!-- ========== 类型声明缩进配置 ========== -->
    <!-- 类体声明相对于类头部缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.indent_body_declarations_compare_to_type_header" value="true"/>
    <!-- 枚举体声明相对于枚举头部缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.indent_body_declarations_compare_to_enum_declaration_header" value="true"/>
    <!-- 枚举常量体声明相对于枚举常量头部缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.indent_body_declarations_compare_to_enum_constant_header" value="true"/>
    <!-- 注解体声明相对于注解头部缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.indent_body_declarations_compare_to_annotation_declaration_header" value="true"/>
    <!-- Record体声明相对于Record头部缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.indent_body_declarations_compare_to_record_header" value="true"/>

    <!-- ========== 语句缩进配置 ========== -->
    <!-- 语句相对于方法体缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.indent_statements_compare_to_body" value="true"/>
    <!-- 语句相对于代码块缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.indent_statements_compare_to_block" value="true"/>
    <!-- switch语句相对于switch关键字缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.indent_switchstatements_compare_to_switch" value="true"/>
    <!-- switch语句相对于case标签缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.indent_switchstatements_compare_to_cases" value="true"/>
    <!-- break语句相对于case标签缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.indent_breaks_compare_to_cases" value="true"/>
    <!-- 不缩进空行 -->
    <setting id="org.eclipse.jdt.core.formatter.indent_empty_lines" value="false"/>

    <!-- ========== 对齐配置 ========== -->
    <!-- 不按列对齐类型成员 -->
    <setting id="org.eclipse.jdt.core.formatter.align_type_members_on_columns" value="false"/>
    <!-- 不按列对齐变量声明 -->
    <setting id="org.eclipse.jdt.core.formatter.align_variable_declarations_on_columns" value="false"/>
    <!-- 不按列对齐赋值语句 -->
    <setting id="org.eclipse.jdt.core.formatter.align_assignment_statements_on_columns" value="false"/>
    <!-- 使用空格进行对齐 -->
    <setting id="org.eclipse.jdt.core.formatter.align_with_spaces" value="true"/>
    <!-- 字段分组间的空行数：1行 -->
    <setting id="org.eclipse.jdt.core.formatter.align_fields_grouping_blank_lines" value="1"/>

    <!-- ========== 大括号位置配置 ========== -->
    <!-- 类声明的大括号位置：行尾 -->
    <setting id="org.eclipse.jdt.core.formatter.brace_position_for_type_declaration" value="end_of_line"/>
    <!-- 匿名类声明的大括号位置：行尾 -->
    <setting id="org.eclipse.jdt.core.formatter.brace_position_for_anonymous_type_declaration" value="end_of_line"/>
    <!-- 构造函数声明的大括号位置：行尾 -->
    <setting id="org.eclipse.jdt.core.formatter.brace_position_for_constructor_declaration" value="end_of_line"/>
    <!-- 方法声明的大括号位置：行尾 -->
    <setting id="org.eclipse.jdt.core.formatter.brace_position_for_method_declaration" value="end_of_line"/>
    <!-- 枚举声明的大括号位置：行尾 -->
    <setting id="org.eclipse.jdt.core.formatter.brace_position_for_enum_declaration" value="end_of_line"/>
    <!-- 枚举常量的大括号位置：行尾 -->
    <setting id="org.eclipse.jdt.core.formatter.brace_position_for_enum_constant" value="end_of_line"/>
    <!-- Record声明的大括号位置：行尾 -->
    <setting id="org.eclipse.jdt.core.formatter.brace_position_for_record_declaration" value="end_of_line"/>
    <!-- Record构造函数的大括号位置：行尾 -->
    <setting id="org.eclipse.jdt.core.formatter.brace_position_for_record_constructor" value="end_of_line"/>
    <!-- 注解类型声明的大括号位置：行尾 -->
    <setting id="org.eclipse.jdt.core.formatter.brace_position_for_annotation_type_declaration" value="end_of_line"/>
    <!-- 代码块的大括号位置：行尾 -->
    <setting id="org.eclipse.jdt.core.formatter.brace_position_for_block" value="end_of_line"/>
    <!-- case块中的大括号位置：行尾 -->
    <setting id="org.eclipse.jdt.core.formatter.brace_position_for_block_in_case" value="end_of_line"/>
    <!-- switch语句的大括号位置：行尾 -->
    <setting id="org.eclipse.jdt.core.formatter.brace_position_for_switch" value="end_of_line"/>
    <!-- 数组初始化器的大括号位置：行尾 -->
    <setting id="org.eclipse.jdt.core.formatter.brace_position_for_array_initializer" value="end_of_line"/>
    <!-- 保持空数组初始化器在一行 -->
    <setting id="org.eclipse.jdt.core.formatter.keep_empty_array_initializer_on_one_line" value="true"/>
    <!-- Lambda表达式体的大括号位置：行尾 -->
    <setting id="org.eclipse.jdt.core.formatter.brace_position_for_lambda_body" value="end_of_line"/>

    <!-- ========== 括号位置配置 ========== -->
    <!-- 方法声明中的括号位置：保持原有位置 -->
    <setting id="org.eclipse.jdt.core.formatter.parentheses_positions_in_method_delcaration" value="preserve_positions"/>
    <!-- 方法调用中的括号位置：保持原有位置 -->
    <setting id="org.eclipse.jdt.core.formatter.parentheses_positions_in_method_invocation" value="preserve_positions"/>
    <!-- 枚举常量声明中的括号位置：保持原有位置 -->
    <setting id="org.eclipse.jdt.core.formatter.parentheses_positions_in_enum_constant_declaration" value="preserve_positions"/>
    <!-- Record声明中的括号位置：换行时分行显示 -->
    <setting id="org.eclipse.jdt.core.formatter.parentheses_positions_in_record_declaration" value="separate_lines_if_wrapped"/>
    <!-- 注解中的括号位置：保持原有位置 -->
    <setting id="org.eclipse.jdt.core.formatter.parentheses_positions_in_annotation" value="preserve_positions"/>
    <!-- Lambda声明中的括号位置：保持原有位置 -->
    <setting id="org.eclipse.jdt.core.formatter.parentheses_positions_in_lambda_declaration" value="preserve_positions"/>
    <!-- if/while语句中的括号位置：保持原有位置 -->
    <setting id="org.eclipse.jdt.core.formatter.parentheses_positions_in_if_while_statement" value="preserve_positions"/>
    <!-- for语句中的括号位置：保持原有位置 -->
    <setting id="org.eclipse.jdt.core.formatter.parentheses_positions_in_for_statment" value="preserve_positions"/>
    <!-- switch语句中的括号位置：保持原有位置 -->
    <setting id="org.eclipse.jdt.core.formatter.parentheses_positions_in_switch_statement" value="preserve_positions"/>
    <!-- try子句中的括号位置：保持原有位置 -->
    <setting id="org.eclipse.jdt.core.formatter.parentheses_positions_in_try_clause" value="preserve_positions"/>
    <!-- catch子句中的括号位置：保持原有位置 -->
    <setting id="org.eclipse.jdt.core.formatter.parentheses_positions_in_catch_clause" value="preserve_positions"/>

    <!-- ========== 空格插入配置 - 类型声明 ========== -->
    <!-- 类型声明的大括号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_type_declaration" value="insert"/>
    <!-- 匿名类型声明的大括号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_anonymous_type_declaration" value="insert"/>
    <!-- 父接口列表中逗号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_superinterfaces" value="do not insert"/>
    <!-- 父接口列表中逗号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_superinterfaces" value="insert"/>
    <!-- 多字段声明中逗号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_multiple_field_declarations" value="do not insert"/>
    <!-- 多字段声明中逗号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_multiple_field_declarations" value="insert"/>
    <!-- 多局部变量声明中逗号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_multiple_local_declarations" value="do not insert"/>
    <!-- 多局部变量声明中逗号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_multiple_local_declarations" value="insert"/>

    <!-- ========== 空格插入配置 - 构造函数 ========== -->
    <!-- 构造函数声明的左括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_constructor_declaration" value="do not insert"/>
    <!-- 构造函数声明的左括号后不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_constructor_declaration" value="do not insert"/>
    <!-- 构造函数声明的右括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_constructor_declaration" value="do not insert"/>
    <!-- 构造函数声明的空括号间不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_between_empty_parens_in_constructor_declaration" value="do not insert"/>
    <!-- 构造函数声明的大括号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_constructor_declaration" value="insert"/>
    <!-- 构造函数参数列表中逗号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_constructor_declaration_parameters" value="do not insert"/>
    <!-- 构造函数参数列表中逗号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_constructor_declaration_parameters" value="insert"/>
    <!-- 构造函数throws子句中逗号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_constructor_declaration_throws" value="do not insert"/>
    <!-- 构造函数throws子句中逗号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_constructor_declaration_throws" value="insert"/>

    <!-- ========== 空格插入配置 - 方法声明 ========== -->
    <!-- 方法声明的左括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_method_declaration" value="do not insert"/>
    <!-- 方法声明的左括号后不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_method_declaration" value="do not insert"/>
    <!-- 方法声明的右括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_method_declaration" value="do not insert"/>
    <!-- 方法声明的空括号间不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_between_empty_parens_in_method_declaration" value="do not insert"/>
    <!-- 方法声明的大括号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_method_declaration" value="insert"/>
    <!-- 方法参数列表中逗号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_method_declaration_parameters" value="do not insert"/>
    <!-- 方法参数列表中逗号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_method_declaration_parameters" value="insert"/>
    <!-- 省略号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_ellipsis" value="do not insert"/>
    <!-- 省略号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_ellipsis" value="insert"/>
    <!-- 方法throws子句中逗号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_method_declaration_throws" value="do not insert"/>
    <!-- 方法throws子句中逗号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_method_declaration_throws" value="insert"/>

    <!-- ========== 空格插入配置 - 标签语句 ========== -->
    <!-- 标签语句中冒号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_colon_in_labeled_statement" value="do not insert"/>
    <!-- 标签语句中冒号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_colon_in_labeled_statement" value="insert"/>

    <!-- ========== 空格插入配置 - 注解 ========== -->
    <!-- 注解中@符号后不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_at_in_annotation" value="do not insert"/>
    <!-- 注解的左括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_annotation" value="do not insert"/>
    <!-- 注解的左括号后不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_annotation" value="do not insert"/>
    <!-- 注解中逗号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_annotation" value="do not insert"/>
    <!-- 注解中逗号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_annotation" value="insert"/>
    <!-- 注解的右括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_annotation" value="do not insert"/>

    <!-- ========== 空格插入配置 - 枚举 ========== -->
    <!-- 枚举声明的大括号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_enum_declaration" value="insert"/>
    <!-- 枚举声明中逗号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_enum_declarations" value="do not insert"/>
    <!-- 枚举声明中逗号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_enum_declarations" value="insert"/>
    <!-- 枚举常量的左括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_enum_constant" value="do not insert"/>
    <!-- 枚举常量的左括号后不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_enum_constant" value="do not insert"/>
    <!-- 枚举常量的空括号间不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_between_empty_parens_in_enum_constant" value="do not insert"/>
    <!-- 枚举常量参数中逗号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_enum_constant_arguments" value="do not insert"/>
    <!-- 枚举常量参数中逗号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_enum_constant_arguments" value="insert"/>
    <!-- 枚举常量的右括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_enum_constant" value="do not insert"/>
    <!-- 枚举常量的大括号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_enum_constant" value="insert"/>

    <!-- ========== 空格插入配置 - 注解类型 ========== -->
    <!-- 注解类型声明中@符号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_at_in_annotation_type_declaration" value="insert"/>
    <!-- 注解类型声明中@符号后不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_at_in_annotation_type_declaration" value="do not insert"/>
    <!-- 注解类型声明的大括号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_annotation_type_declaration" value="insert"/>
    <!-- 注解类型成员声明的左括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_annotation_type_member_declaration" value="do not insert"/>
    <!-- 注解类型成员声明的空括号间不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_between_empty_parens_in_annotation_type_member_declaration" value="do not insert"/>

    <!-- ========== 空格插入配置 - Record ========== -->
    <!-- Record声明的左括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_record_declaration" value="do not insert"/>
    <!-- Record声明的左括号后不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_record_declaration" value="do not insert"/>
    <!-- Record组件中逗号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_record_components" value="do not insert"/>
    <!-- Record组件中逗号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_record_components" value="insert"/>
    <!-- Record声明的右括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_record_declaration" value="do not insert"/>
    <!-- Record声明的大括号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_record_declaration" value="insert"/>
    <!-- Record构造函数的大括号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_record_constructor" value="insert"/>

    <!-- ========== 空格插入配置 - Lambda表达式 ========== -->
    <!-- Lambda箭头前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_lambda_arrow" value="insert"/>
    <!-- Lambda箭头后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_lambda_arrow" value="insert"/>

    <!-- ========== 空格插入配置 - 代码块 ========== -->
    <!-- 代码块的大括号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_block" value="insert"/>
    <!-- 代码块的右大括号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_closing_brace_in_block" value="insert"/>

    <!-- ========== 空格插入配置 - 控制流语句 ========== -->
    <!-- if语句的左括号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_if" value="insert"/>
    <!-- if语句的左括号后不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_if" value="do not insert"/>
    <!-- if语句的右括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_if" value="do not insert"/>

    <!-- for语句的左括号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_for" value="insert"/>
    <!-- for语句的左括号后不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_for" value="do not insert"/>
    <!-- for语句的右括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_for" value="do not insert"/>
    <!-- for语句初始化部分逗号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_for_inits" value="do not insert"/>
    <!-- for语句初始化部分逗号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_for_inits" value="insert"/>
    <!-- for语句递增部分逗号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_for_increments" value="do not insert"/>
    <!-- for语句递增部分逗号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_for_increments" value="insert"/>
    <!-- for语句分号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_semicolon_in_for" value="do not insert"/>
    <!-- for语句分号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_semicolon_in_for" value="insert"/>
    <!-- for-each语句冒号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_colon_in_for" value="insert"/>
    <!-- for-each语句冒号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_colon_in_for" value="insert"/>

    <!-- ========== 空格插入配置 - switch语句 ========== -->
    <!-- case标签冒号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_colon_in_case" value="do not insert"/>
    <!-- default标签冒号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_colon_in_default" value="do not insert"/>
    <!-- switch case箭头前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_arrow_in_switch_case" value="insert"/>
    <!-- switch case箭头后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_arrow_in_switch_case" value="insert"/>
    <!-- switch default箭头前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_arrow_in_switch_default" value="insert"/>
    <!-- switch default箭头后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_arrow_in_switch_default" value="insert"/>
    <!-- case标签冒号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_colon_in_case" value="insert"/>
    <!-- switch case表达式中逗号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_switch_case_expressions" value="do not insert"/>
    <!-- switch case表达式中逗号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_switch_case_expressions" value="insert"/>
    <!-- switch语句的左括号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_switch" value="insert"/>
    <!-- switch语句的左括号后不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_switch" value="do not insert"/>
    <!-- switch语句的右括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_switch" value="do not insert"/>
    <!-- switch语句的大括号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_switch" value="insert"/>

    <!-- while语句的左括号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_while" value="insert"/>
    <!-- while语句的左括号后不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_while" value="do not insert"/>
    <!-- while语句的右括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_while" value="do not insert"/>

    <!-- synchronized语句的左括号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_synchronized" value="insert"/>
    <!-- synchronized语句的左括号后不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_synchronized" value="do not insert"/>
    <!-- synchronized语句的右括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_synchronized" value="do not insert"/>

    <!-- ========== 空格插入配置 - 异常处理 ========== -->
    <!-- try语句的左括号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_try" value="insert"/>
    <!-- try语句的左括号后不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_try" value="do not insert"/>
    <!-- try资源中分号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_semicolon_in_try_resources" value="do not insert"/>
    <!-- try资源中分号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_semicolon_in_try_resources" value="insert"/>
    <!-- try语句的右括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_try" value="do not insert"/>
    <!-- catch语句的左括号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_catch" value="insert"/>
    <!-- catch语句的左括号后不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_catch" value="do not insert"/>
    <!-- catch语句的右括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_catch" value="do not insert"/>

    <!-- ========== 空格插入配置 - 断言和语句 ========== -->
    <!-- assert语句冒号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_colon_in_assert" value="insert"/>
    <!-- assert语句冒号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_colon_in_assert" value="insert"/>
    <!-- return语句中括号表达式前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_parenthesized_expression_in_return" value="insert"/>
    <!-- throw语句中括号表达式前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_parenthesized_expression_in_throw" value="insert"/>
    <!-- 分号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_semicolon" value="do not insert"/>

    <!-- ========== 空格插入配置 - 方法调用 ========== -->
    <!-- 方法调用的左括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_method_invocation" value="do not insert"/>
    <!-- 方法调用的左括号后不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_method_invocation" value="do not insert"/>
    <!-- 方法调用的右括号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_method_invocation" value="do not insert"/>
    <!-- 方法调用的空括号间不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_between_empty_parens_in_method_invocation" value="do not insert"/>
    <!-- 方法调用参数中逗号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_method_invocation_arguments" value="do not insert"/>
    <!-- 方法调用参数中逗号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_method_invocation_arguments" value="insert"/>

    <!-- ========== 空格插入配置 - 对象分配 ========== -->
    <!-- 对象分配表达式中逗号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_allocation_expression" value="do not insert"/>
    <!-- 对象分配表达式中逗号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_allocation_expression" value="insert"/>
    <!-- 显式构造函数调用参数中逗号前不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_explicitconstructorcall_arguments" value="do not insert"/>
    <!-- 显式构造函数调用参数中逗号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_explicitconstructorcall_arguments" value="insert"/>

    <!-- ========== 空格插入配置 - 操作符 ========== -->
    <!-- 后缀操作符前不插入空格 (如 i++) -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_postfix_operator" value="do not insert"/>
    <!-- 后缀操作符后不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_postfix_operator" value="do not insert"/>
    <!-- 前缀操作符前不插入空格 (如 ++i) -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_prefix_operator" value="do not insert"/>
    <!-- 前缀操作符后不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_prefix_operator" value="do not insert"/>
    <!-- 一元操作符前不插入空格 (如 -x) -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_unary_operator" value="do not insert"/>
    <!-- 一元操作符后不插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_unary_operator" value="do not insert"/>
    <!-- 非操作符后不插入空格 (如 !flag) -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_not_operator" value="do not insert"/>

    <!-- ========== 空格插入配置 - 二元操作符 ========== -->
    <!-- 乘法操作符前插入空格 (*, /, %) -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_multiplicative_operator" value="insert"/>
    <!-- 乘法操作符后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_multiplicative_operator" value="insert"/>
    <!-- 加法操作符前插入空格 (+, -) -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_additive_operator" value="insert"/>
    <!-- 加法操作符后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_additive_operator" value="insert"/>
    <!-- 字符串连接操作符前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_string_concatenation" value="insert"/>
    <!-- 字符串连接操作符后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_string_concatenation" value="insert"/>
    <!-- 位移操作符前插入空格 (<<, >>, >>>) -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_shift_operator" value="insert"/>
    <!-- 位移操作符后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_shift_operator" value="insert"/>
    <!-- 关系操作符前插入空格 (<, >, <=, >=, instanceof) -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_relational_operator" value="insert"/>
    <!-- 关系操作符后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_relational_operator" value="insert"/>
    <!-- 位操作符前插入空格 (&, |, ^) -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_bitwise_operator" value="insert"/>
    <!-- 位操作符后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_bitwise_operator" value="insert"/>
    <!-- 逻辑操作符前插入空格 (&&, ||) -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_logical_operator" value="insert"/>
    <!-- 逻辑操作符后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_logical_operator" value="insert"/>

    <!-- ========== 空格插入配置 - 条件表达式 ========== -->
    <!-- 三元操作符问号前插入空格 (condition ? true : false) -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_question_in_conditional" value="insert"/>
    <!-- 三元操作符问号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_question_in_conditional" value="insert"/>
    <!-- 三元操作符冒号前插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_colon_in_conditional" value="insert"/>
    <!-- 三元操作符冒号后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_colon_in_conditional" value="insert"/>

    <!-- ========== 空格插入配置 - 赋值操作符 ========== -->
    <!-- 赋值操作符前插入空格 (=, +=, -=, 等) -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_assignment_operator" value="insert"/>
    <!-- 赋值操作符后插入空格 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_assignment_operator" value="insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_paren_in_parenthesized_expression" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_parenthesized_expression" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_parenthesized_expression" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_paren_in_cast" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_paren_in_cast" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_closing_paren_in_cast" value="insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_bracket_in_array_type_reference" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_between_brackets_in_array_type_reference" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_bracket_in_array_allocation_expression" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_bracket_in_array_allocation_expression" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_bracket_in_array_allocation_expression" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_between_empty_brackets_in_array_allocation_expression" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_brace_in_array_initializer" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_brace_in_array_initializer" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_brace_in_array_initializer" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_array_initializer" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_array_initializer" value="insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_between_empty_braces_in_array_initializer" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_bracket_in_array_reference" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_bracket_in_array_reference" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_bracket_in_array_reference" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_angle_bracket_in_parameterized_type_reference" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_angle_bracket_in_parameterized_type_reference" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_parameterized_type_reference" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_parameterized_type_reference" value="insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_angle_bracket_in_parameterized_type_reference" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_angle_bracket_in_type_arguments" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_angle_bracket_in_type_arguments" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_type_arguments" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_type_arguments" value="insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_angle_bracket_in_type_arguments" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_closing_angle_bracket_in_type_arguments" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_opening_angle_bracket_in_type_parameters" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_opening_angle_bracket_in_type_parameters" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_comma_in_type_parameters" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_comma_in_type_parameters" value="insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_closing_angle_bracket_in_type_parameters" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_closing_angle_bracket_in_type_parameters" value="insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_and_in_type_parameter" value="insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_and_in_type_parameter" value="insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_before_question_in_wildcard" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_space_after_question_in_wildcard" value="do not insert"/>

    <!-- ========== 空行配置 ========== -->
    <!-- 保留的空行数量：最多1行 -->
    <setting id="org.eclipse.jdt.core.formatter.number_of_empty_lines_to_preserve" value="1"/>
    <!-- package声明前的空行数：0行 -->
    <setting id="org.eclipse.jdt.core.formatter.blank_lines_before_package" value="0"/>
    <!-- package声明后的空行数：1行 -->
    <setting id="org.eclipse.jdt.core.formatter.blank_lines_after_package" value="1"/>
    <!-- import语句前的空行数：1行 -->
    <setting id="org.eclipse.jdt.core.formatter.blank_lines_before_imports" value="1"/>
    <!-- import语句后的空行数：1行 -->
    <setting id="org.eclipse.jdt.core.formatter.blank_lines_after_imports" value="1"/>
    <!-- 类型声明之间的空行数：1行 -->
    <setting id="org.eclipse.jdt.core.formatter.blank_lines_between_type_declarations" value="1"/>
    <!-- 类体第一个声明前的空行数：1行 -->
    <setting id="org.eclipse.jdt.core.formatter.blank_lines_before_first_class_body_declaration" value="1"/>
    <!-- 类体最后一个声明后的空行数：0行 -->
    <setting id="org.eclipse.jdt.core.formatter.blank_lines_after_last_class_body_declaration" value="0"/>
    <!-- 新代码块前的空行数：1行 -->
    <setting id="org.eclipse.jdt.core.formatter.blank_lines_before_new_chunk" value="1"/>
    <!-- 成员类型前的空行数：1行 -->
    <setting id="org.eclipse.jdt.core.formatter.blank_lines_before_member_type" value="1"/>
    <!-- 字段前的空行数：0行 -->
    <setting id="org.eclipse.jdt.core.formatter.blank_lines_before_field" value="0"/>
    <!-- 抽象方法前的空行数：1行 -->
    <setting id="org.eclipse.jdt.core.formatter.blank_lines_before_abstract_method" value="1"/>
    <!-- 方法前的空行数：1行 -->
    <setting id="org.eclipse.jdt.core.formatter.blank_lines_before_method" value="1"/>
    <!-- 方法体开始处的空行数：0行 -->
    <setting id="org.eclipse.jdt.core.formatter.number_of_blank_lines_at_beginning_of_method_body" value="0"/>

    <!-- ========== 换行配置 ========== -->
    <!-- 空语句放在新行：是 -->
    <setting id="org.eclipse.jdt.core.formatter.put_empty_statement_on_new_line" value="true"/>
    <!-- 数组初始化器左大括号后不插入换行 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_new_line_after_opening_brace_in_array_initializer" value="do not insert"/>
    <!-- 数组初始化器右大括号前不插入换行 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_new_line_before_closing_brace_in_array_initializer" value="do not insert"/>
    <!-- 文件末尾缺少换行时不自动插入 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_new_line_at_end_of_file_if_missing" value="do not insert"/>
    <!-- if语句的else前不插入换行 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_new_line_before_else_in_if_statement" value="do not insert"/>
    <!-- try语句的catch前不插入换行 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_new_line_before_catch_in_try_statement" value="do not insert"/>
    <!-- try语句的finally前不插入换行 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_new_line_before_finally_in_try_statement" value="do not insert"/>
    <!-- do-while语句的while前不插入换行 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_new_line_before_while_in_do_statement" value="do not insert"/>
    <!-- 标签后插入换行 -->
    <setting id="org.eclipse.jdt.core.formatter.insert_new_line_after_label" value="insert"/>

    <!-- ========== 语句保持配置 ========== -->
    <!-- then语句不保持在同一行 -->
    <setting id="org.eclipse.jdt.core.formatter.keep_then_statement_on_same_line" value="false"/>
    <!-- 简单if语句不保持在一行 -->
    <setting id="org.eclipse.jdt.core.formatter.keep_imple_if_on_one_line" value="false"/>
    <!-- else语句不保持在同一行 -->
    <setting id="org.eclipse.jdt.core.formatter.keep_else_statement_on_same_line" value="false"/>
    <!-- 紧凑的else if格式：是 -->
    <setting id="org.eclipse.jdt.core.formatter.compact_else_if" value="true"/>
    <!-- 简单for循环体不保持在同一行 -->
    <setting id="org.eclipse.jdt.core.formatter.keep_simple_for_body_on_same_line" value="false"/>
    <!-- 简单while循环体不保持在同一行 -->
    <setting id="org.eclipse.jdt.core.formatter.keep_simple_while_body_on_same_line" value="false"/>
    <!-- 简单do-while循环体不保持在同一行 -->
    <setting id="org.eclipse.jdt.core.formatter.keep_simple_do_while_body_on_same_line" value="false"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_new_line_after_annotation_on_package" value="insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_new_line_after_annotation_on_type" value="insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_new_line_after_annotation_on_enum_constant" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_new_line_after_annotation_on_parameter" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_new_line_after_annotation_on_method" value="insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_new_line_after_annotation_on_local_variable" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_new_line_after_type_annotation" value="do not insert"/>
    <setting id="org.eclipse.jdt.core.formatter.insert_new_line_after_annotation_on_field" value="insert"/>
    <setting id="org.eclipse.jdt.core.formatter.keep_loop_body_block_on_one_line" value="one_line_never"/>
    <setting id="org.eclipse.jdt.core.formatter.keep_if_then_body_block_on_one_line" value="one_line_never"/>
    <setting id="org.eclipse.jdt.core.formatter.keep_lambda_body_block_on_one_line" value="one_line_never"/>
    <setting id="org.eclipse.jdt.core.formatter.keep_code_block_on_one_line" value="one_line_never"/>
    <setting id="org.eclipse.jdt.core.formatter.keep_method_body_on_one_line" value="one_line_never"/>
    <setting id="org.eclipse.jdt.core.formatter.keep_simple_getter_setter_on_one_line" value="false"/>
    <setting id="org.eclipse.jdt.core.formatter.keep_type_declaration_on_one_line" value="one_line_never"/>
    <setting id="org.eclipse.jdt.core.formatter.keep_anonymous_type_declaration_on_one_line" value="one_line_never"/>
    <setting id="org.eclipse.jdt.core.formatter.keep_enum_declaration_on_one_line" value="one_line_never"/>
    <setting id="org.eclipse.jdt.core.formatter.keep_enum_constant_declaration_on_one_line" value="one_line_never"/>
    <setting id="org.eclipse.jdt.core.formatter.keep_record_declaration_on_one_line" value="one_line_never"/>
    <setting id="org.eclipse.jdt.core.formatter.keep_record_constructor_on_one_line" value="one_line_never"/>
    <setting id="org.eclipse.jdt.core.formatter.keep_annotation_declaration_on_one_line" value="one_line_never"/>

    <!-- ========== 换行长度和缩进配置 ========== -->
    <!-- 代码行最大长度：210个字符 -->
    <setting id="org.eclipse.jdt.core.formatter.lineSplit" value="210"/>
    <!-- 续行缩进：1个缩进单位 -->
    <setting id="org.eclipse.jdt.core.formatter.continuation_indentation" value="1"/>
    <!-- 数组初始化器续行缩进：1个缩进单位 -->
    <setting id="org.eclipse.jdt.core.formatter.continuation_indentation_for_array_initializer" value="1"/>
    <!-- 不合并已换行的代码 -->
    <setting id="org.eclipse.jdt.core.formatter.join_wrapped_lines" value="false"/>
    <!-- 嵌套时不换行外层表达式 -->
    <setting id="org.eclipse.jdt.core.formatter.wrap_outer_expressions_when_nested" value="false"/>

    <!-- ========== 对齐配置 ========== -->
    <!-- 类型声明中父类的对齐：换行时缩进 (16 = 换行时缩进) -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_superclass_in_type_declaration" value="16"/>
    <!-- 类型声明中父接口的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_superinterfaces_in_type_declaration" value="16"/>
    <!-- 多字段声明的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_multiple_fields" value="16"/>
    <!-- 构造函数参数的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_parameters_in_constructor_declaration" value="16"/>
    <!-- 构造函数throws子句的对齐：不换行 (0 = 不换行) -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_throws_clause_in_constructor_declaration" value="0"/>
    <!-- 方法声明的对齐：不换行 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_method_declaration" value="0"/>
    <!-- 方法throws子句的对齐：不换行 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_throws_clause_in_method_declaration" value="0"/>
    <!-- 方法参数的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_parameters_in_method_declaration" value="16"/>
    <!-- 枚举常量的对齐：换行并缩进第一个元素 (49 = 换行并缩进第一个元素) -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_enum_constants" value="49"/>
    <!-- 枚举常量参数的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_arguments_in_enum_constant" value="16"/>
    <!-- 枚举声明中父接口的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_superinterfaces_in_enum_declaration" value="16"/>
    <!-- Record组件的对齐：换行并缩进除第一个外的所有元素 (81 = 换行并缩进除第一个外的所有元素) -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_record_components" value="81"/>
    <!-- Record声明中父接口的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_superinterfaces_in_record_declaration" value="16"/>
    <!-- 方法调用参数的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_arguments_in_method_invocation" value="16"/>
    <!-- 方法调用选择器的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_selector_in_method_invocation" value="16"/>
    <!-- 显式构造函数调用参数的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_arguments_in_explicit_constructor_call" value="16"/>
    <!-- 对象分配表达式参数的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_arguments_in_allocation_expression" value="16"/>
    <!-- 限定对象分配表达式参数的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_arguments_in_qualified_allocation_expression" value="16"/>

    <!-- ========== 操作符对齐和换行配置 ========== -->
    <!-- 乘法操作符的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_multiplicative_operator" value="16"/>
    <!-- 乘法操作符前换行 -->
    <setting id="org.eclipse.jdt.core.formatter.wrap_before_multiplicative_operator" value="true"/>
    <!-- 加法操作符的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_additive_operator" value="16"/>
    <!-- 加法操作符前换行 -->
    <setting id="org.eclipse.jdt.core.formatter.wrap_before_additive_operator" value="true"/>
    <!-- 字符串连接的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_string_concatenation" value="16"/>
    <!-- 字符串连接操作符前换行 -->
    <setting id="org.eclipse.jdt.core.formatter.wrap_before_string_concatenation" value="true"/>
    <!-- 位移操作符的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_shift_operator" value="16"/>
    <!-- 位移操作符前换行 -->
    <setting id="org.eclipse.jdt.core.formatter.wrap_before_shift_operator" value="true"/>
    <!-- 关系操作符的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_relational_operator" value="16"/>
    <!-- 关系操作符前换行 -->
    <setting id="org.eclipse.jdt.core.formatter.wrap_before_relational_operator" value="true"/>
    <!-- 位操作符的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_bitwise_operator" value="16"/>
    <!-- 位操作符前换行 -->
    <setting id="org.eclipse.jdt.core.formatter.wrap_before_bitwise_operator" value="true"/>
    <!-- 逻辑操作符的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_logical_operator" value="16"/>
    <!-- 逻辑操作符前换行 -->
    <setting id="org.eclipse.jdt.core.formatter.wrap_before_logical_operator" value="true"/>
    <!-- 条件表达式的对齐：换行时缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_conditional_expression" value="16"/>
    <!-- 条件表达式链的对齐：不换行 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_conditional_expression_chain" value="0"/>
    <!-- 条件操作符前换行 -->
    <setting id="org.eclipse.jdt.core.formatter.wrap_before_conditional_operator" value="true"/>
    <!-- 赋值的对齐：不换行 -->
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_assignment" value="0"/>
    <!-- 赋值操作符前不换行 -->
    <setting id="org.eclipse.jdt.core.formatter.wrap_before_assignment_operator" value="false"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_expressions_in_array_initializer" value="16"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_expressions_in_for_loop_header" value="16"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_compact_if" value="16"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_compact_loops" value="16"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_resources_in_try" value="0"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_union_type_in_multicatch" value="18"/>
    <setting id="org.eclipse.jdt.core.formatter.wrap_before_or_operator_multicatch" value="false"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_assertion_message" value="0"/>
    <setting id="org.eclipse.jdt.core.formatter.wrap_before_assertion_message_operator" value="false"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_parameterized_type_references" value="0"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_type_arguments" value="0"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_type_parameters" value="0"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_annotations_on_package" value="0"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_annotations_on_type" value="49"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_annotations_on_enum_constant" value="0"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_annotations_on_field" value="49"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_annotations_on_method" value="16"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_annotations_on_local_variable" value="0"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_annotations_on_parameter" value="0"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_type_annotations" value="49"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_arguments_in_annotation" value="0"/>
    <setting id="org.eclipse.jdt.core.formatter.alignment_for_module_statements" value="0"/>

    <!-- ========== 注释格式化配置 ========== -->
    <!-- 注释行长度：210个字符 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.line_length" value="210"/>
    <!-- 不从起始位置计算行长度 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.count_line_length_from_starting_position" value="false"/>
    <!-- 格式化Javadoc注释：是 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.format_javadoc_comments" value="true"/>
    <!-- 格式化块注释：否 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.format_block_comments" value="false"/>
    <!-- 格式化行注释：否 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.format_line_comments" value="false"/>
    <!-- 不格式化第一列开始的行注释 -->
    <setting id="org.eclipse.jdt.core.formatter.format_line_comment_starting_on_first_column" value="false"/>
    <!-- 格式化注释头部：是 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.format_header" value="true"/>
    <!-- 保留代码和行注释之间的空白 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.preserve_white_space_between_code_and_line_comments" value="true"/>
    <!-- 第一列的行注释不缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.never_indent_line_comments_on_first_column" value="true"/>
    <!-- 第一列的块注释可以缩进 -->
    <setting id="org.eclipse.jdt.core.formatter.never_indent_block_comments_on_first_column" value="false"/>
    <!-- 不合并注释中的行 -->
    <setting id="org.eclipse.jdt.core.formatter.join_lines_in_comments" value="false"/>
    <!-- 不格式化HTML：否 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.format_html" value="false"/>
    <!-- 不格式化源代码：否 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.format_source_code" value="false"/>
    <!-- 根标签前插入换行 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.insert_new_line_before_root_tags" value="insert"/>
    <!-- 不同标签间不插入换行 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.insert_new_line_between_different_tags" value="do not insert"/>
    <!-- 不对齐标签名称和描述 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.align_tags_names_descriptions" value="false"/>
    <!-- 对齐分组的标签描述 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.align_tags_descriptions_grouped" value="true"/>
    <!-- 参数标签不换行 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.insert_new_line_for_parameter" value="do not insert"/>
    <!-- 不缩进参数描述 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.indent_parameter_description" value="false"/>
    <!-- 不缩进标签描述 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.indent_tag_description" value="false"/>
    <!-- 不缩进根标签 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.indent_root_tags" value="false"/>
    <!-- Javadoc边界处换行 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.new_lines_at_javadoc_boundaries" value="true"/>
    <!-- 不清除Javadoc中的空行 -->
    <setting id="org.eclipse.jdt.core.formatter.comment.clear_blank_lines_in_javadoc_comment" value="false"/>

    <!-- ========== 格式化开关标签配置 ========== -->
    <!-- 使用开关标签：是 -->
    <setting id="org.eclipse.jdt.core.formatter.use_on_off_tags" value="true"/>
    <!-- 启用格式化标签：@formatter:on -->
    <setting id="org.eclipse.jdt.core.formatter.enabling_tag" value="@formatter:on"/>
    <!-- 禁用格式化标签：@formatter:off -->
    <setting id="org.eclipse.jdt.core.formatter.disabling_tag" value="@formatter:off"/>
  </profile>
</profiles>
