--- ### 项目配置
project:
  # URL（跨域配置默认放行此 URL，第三方登录回调默认使用此 URL 为前缀，请注意更改为你实际的前端 URL）
  url: http://localhost:3000
--- ### 数据源配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: jdbc:p6spy:mysql://${DB_HOST:************}:${DB_PORT:3308}/${DB_NAME:fulfillmen_shop}?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf8&useSSL=false&allowMultiQueries=true&rewriteBatchedStatements=true&autoReconnect=true&maxReconnects=10&failOverReadOnly=false
    username: ${DB_USER:root}
    password: ${DB_PWD:root}
    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
  # 线程池配置 虚拟线程开启
  # threads:
  #   virtual:
  #     enabled: true
  data:
    ## Redis 配置（单机模式）
    redis:
      # 地址
      host: ${REDIS_HOST:************}
      # 端口（默认 6379）
      port: ${REDIS_PORT:6379}
      # 密码（未设置密码时请注释掉）
      password: ${REDIS_PWD:123456}
      # 数据库索引 10 号库
      database: ${REDIS_DB:0}
      # 连接超时时间
      timeout: 10s
    ## Redisson 配置
    redisson:
      enabled: true
      mode: SINGLE
  ## Liquibase 配置
  liquibase:
    # 是否启用
    enabled: false
    # 配置文件路径
    change-log: classpath:/db/changelog/db.changelog-master.yaml
--- ### fulfillmen starter
fulfillmen-starter:
  ### 跨域配置
  web.cors:
    enabled: true
    # 配置允许跨域的域名
    allowed-origins: "*"
    # 配置允许跨域的请求方式
    allowed-methods: "*"
    # 配置允许跨域的请求头
    allowed-headers: "*"
    # 配置允许跨域的响应头
    exposed-headers: "*"
    # 是否允许发送 Cookie
    allow-credentials: false
  log:
    # 是否打印日志，开启后可打印访问日志（类似于 Nginx access log）
    is-print: true
    exclude-patterns:
      - /**.css
      - /**.html
      - /**.js
## 项目日志配置（配置重叠部分，优先级高于 logback-spring.xml 中的配置）
logging:
  level:
    com.fulfillmen.shop: DEBUG
    com.fulfillmen.starter: DEBUG
  file:
    path: ./logs
--- ### 接口文档配置
springdoc:
  # 开发、测试，默认开启
  swagger-ui:
    enabled: true
--- ### Just Auth 配置
justauth:
  enabled: false
  type:
    GITEE:
      client-id: 5d271b7f638941812aaf8bfc2e2f08f06d6235ef934e0e39537e2364eb8452c4
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${project.url}/social/callback?source=gitee
    GITHUB:
      client-id: 38080dad08cfbdfacca9
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${project.url}/social/callback?source=github
  cache:
    type: REDIS

## 头像支持格式配置
avatar:
  support-suffix: jpg,jpeg,png,gif

## 接口文档增强配置
knife4j:
  enable: true
  # 开启密码验证
  basic:
    enable: false
    username: admin
    password: admin

fulfillmen:
  wms:
    base-url: http://192.168.33.18:8888
