# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
end_of_line = lf
insert_final_newline = true
charset = utf-8
trim_trailing_whitespace = true
indent_style = space
indent_size = 4
max_line_length = 210
continuation_indent_size = 4


# XML files
[*.xml]
indent_style = space
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# JSON files
[*.json]
indent_style = space
indent_size = 2

# Properties files
[*.properties]
indent_style = space
indent_size = 2

[*]
tab_width = 4
ij_continuation_indent_size = 2
ij_formatter_off_tag = @formatter:off
ij_formatter_on_tag = @formatter:on
ij_formatter_tags_enabled = false
ij_smart_tabs = false
ij_wrap_on_typing = false
ij_java_wrap_on_typing = false

[*.java]
# ij_java_align_consecutive_assignments = false
# ij_java_align_consecutive_variable_declarations = false
# ij_java_align_group_field_declarations = false
# ij_java_align_multiline_annotation_parameters = false
# ij_java_align_multiline_array_initializer_expression = false
# ij_java_align_multiline_assignment = false
# ij_java_align_multiline_binary_operation = false
# ij_java_align_multiline_chained_methods = false
# ij_java_align_multiline_deconstruction_list_components = true
# ij_java_align_multiline_extends_list = false
# ij_java_align_multiline_for = false
# ij_java_align_multiline_method_parentheses = false
# ij_java_align_multiline_parameters = false
# ij_java_align_multiline_parameters_in_calls = false
# ij_java_align_multiline_parenthesized_expression = false
# ij_java_align_multiline_records = true
# ij_java_align_multiline_resources = false
# ij_java_align_multiline_ternary_operation = false
# ij_java_align_multiline_text_blocks = false
# ij_java_align_multiline_throws_list = false
# ij_java_align_subsequent_simple_methods = false
# ij_java_align_throws_keyword = false
# ij_java_align_types_in_multi_catch = true
# ij_java_annotation_new_line_in_record_component = true
# ij_java_annotation_parameter_wrap = off
# ij_java_array_initializer_new_line_after_left_brace = false
# ij_java_array_initializer_right_brace_on_new_line = false
# ij_java_array_initializer_wrap = normal
# ij_java_assert_statement_colon_on_next_line = false
# ij_java_assert_statement_wrap = off
# ij_java_assignment_wrap = off
# ij_java_binary_operation_sign_on_next_line = true
# ij_java_binary_operation_wrap = normal
# ij_java_blank_lines_after_anonymous_class_header = 0
# ij_java_blank_lines_after_class_header = 1
# ij_java_blank_lines_after_imports = 1
# ij_java_blank_lines_after_package = 1
# ij_java_blank_lines_around_class = 1
# ij_java_blank_lines_around_field = 0
# ij_java_blank_lines_around_field_in_interface = 0
# ij_java_blank_lines_around_field_with_annotations = 0
# ij_java_blank_lines_around_initializer = 1
# ij_java_blank_lines_around_method = 1
# ij_java_blank_lines_around_method_in_interface = 1
# ij_java_blank_lines_before_class_end = 0
# ij_java_blank_lines_before_imports = 1
# ij_java_blank_lines_before_method_body = 0
# ij_java_blank_lines_before_package = 0
# ij_java_blank_lines_between_record_components = 0
# ij_java_block_brace_style = end_of_line
# ij_java_block_comment_add_space = false
# ij_java_block_comment_at_first_column = true
# ij_java_builder_methods =
# ij_java_call_parameters_new_line_after_left_paren = false
# ij_java_call_parameters_right_paren_on_new_line = false
# ij_java_call_parameters_wrap = normal
# ij_java_case_statement_on_separate_line = true
# ij_java_catch_on_new_line = false
# ij_java_class_annotation_wrap = off
# ij_java_class_brace_style = end_of_line
# ij_java_class_count_to_use_import_on_demand = 999
# ij_java_class_names_in_javadoc = 1
# ij_java_deconstruction_list_wrap = normal
# ij_java_do_not_indent_top_level_class_members = false
# ij_java_do_not_wrap_after_single_annotation = false
# ij_java_do_not_wrap_after_single_annotation_in_parameter = false
# ij_java_do_while_brace_force = always
# ij_java_doc_add_blank_line_after_description = true
# ij_java_doc_add_blank_line_after_param_comments = false
# ij_java_doc_add_blank_line_after_return = false
# ij_java_doc_add_p_tag_on_empty_lines = true
# ij_java_doc_align_exception_comments = true
# ij_java_doc_align_param_comments = true
# ij_java_doc_do_not_wrap_if_one_line = false
# ij_java_doc_enable_formatting = true
# ij_java_doc_enable_leading_asterisks = true
# ij_java_doc_indent_on_continuation = false
# ij_java_doc_keep_empty_lines = true
# ij_java_doc_keep_empty_parameter_tag = true
# ij_java_doc_keep_empty_return_tag = true
# ij_java_doc_keep_empty_throws_tag = true
# ij_java_doc_keep_invalid_tags = true
# ij_java_doc_param_description_on_new_line = false
# ij_java_doc_preserve_line_breaks = false
# ij_java_doc_use_throws_not_exception_tag = true
# ij_java_else_on_new_line = false
# ij_java_entity_dd_prefix =
# ij_java_entity_dd_suffix = EJB
# ij_java_entity_eb_prefix =
# ij_java_entity_eb_suffix = Bean
# ij_java_entity_hi_prefix =
# ij_java_entity_hi_suffix = Home
# ij_java_entity_lhi_prefix = Local
# ij_java_entity_lhi_suffix = Home
# ij_java_entity_li_prefix = Local
# ij_java_entity_li_suffix =
# ij_java_entity_pk_class = java.lang.String
# ij_java_entity_ri_prefix =
# ij_java_entity_ri_suffix =
# ij_java_entity_vo_prefix =
# ij_java_entity_vo_suffix = VO
# ij_java_enum_constants_wrap = split_into_lines
# ij_java_enum_field_annotation_wrap = off
# ij_java_extends_keyword_wrap = off
# ij_java_extends_list_wrap = normal
# ij_java_field_annotation_wrap = split_into_lines
# ij_java_field_name_prefix =
# ij_java_field_name_suffix =
# ij_java_filter_class_prefix =
# ij_java_filter_class_suffix =
# ij_java_filter_dd_prefix =
# ij_java_filter_dd_suffix =
# ij_java_finally_on_new_line = false
# ij_java_for_brace_force = always
# ij_java_for_statement_new_line_after_left_paren = false
# ij_java_for_statement_right_paren_on_new_line = false
# ij_java_for_statement_wrap = normal
# ij_java_generate_final_locals = false
# ij_java_generate_final_parameters = false
# ij_java_generate_use_type_annotation_before_type = true
# ij_java_if_brace_force = always
# ij_java_imports_layout = @*,$*,|,*
# ij_java_indent_case_from_switch = true
# ij_java_insert_inner_class_imports = true
# ij_java_insert_override_annotation = true
# ij_java_keep_blank_lines_before_right_brace = 2
# ij_java_keep_blank_lines_between_package_declaration_and_header = 2
# ij_java_keep_blank_lines_in_code = 1
# ij_java_keep_blank_lines_in_declarations = 2
# ij_java_keep_builder_methods_indents = false
# ij_java_keep_control_statement_in_one_line = false
# ij_java_keep_first_column_comment = true
# ij_java_keep_indents_on_empty_lines = false
# ij_java_keep_line_breaks = true
# ij_java_keep_multiple_expressions_in_one_line = false
# ij_java_keep_simple_blocks_in_one_line = false
# ij_java_keep_simple_classes_in_one_line = false
# ij_java_keep_simple_lambdas_in_one_line = false
# ij_java_keep_simple_methods_in_one_line = false
# ij_java_label_indent_absolute = false
# ij_java_label_indent_size = 0
# ij_java_lambda_brace_style = end_of_line
# ij_java_layout_on_demand_import_from_same_package_first = true
# ij_java_layout_static_imports_separately = true
# ij_java_line_comment_add_space = false
# ij_java_line_comment_add_space_on_reformat = false
# ij_java_line_comment_at_first_column = true
# ij_java_listener_class_prefix =
# ij_java_listener_class_suffix =
# ij_java_local_variable_name_prefix =
# ij_java_local_variable_name_suffix =
# ij_java_message_dd_prefix =
# ij_java_message_dd_suffix = EJB
# ij_java_message_eb_prefix =
# ij_java_message_eb_suffix = Bean
# ij_java_method_annotation_wrap = split_into_lines
# ij_java_method_brace_style = end_of_line
# ij_java_method_call_chain_wrap = normal
# ij_java_method_parameters_new_line_after_left_paren = false
# ij_java_method_parameters_right_paren_on_new_line = false
# ij_java_method_parameters_wrap = normal
# ij_java_modifier_list_wrap = false
# ij_java_multi_catch_types_wrap = normal
# ij_java_names_count_to_use_import_on_demand = 999
# ij_java_new_line_after_lparen_in_annotation = false
# ij_java_new_line_after_lparen_in_deconstruction_pattern = true
# ij_java_new_line_after_lparen_in_record_header = true
# ij_java_new_line_when_body_is_presented = false
# ij_java_packages_to_use_import_on_demand =
# ij_java_parameter_annotation_wrap = off
# ij_java_parameter_name_prefix =
# ij_java_parameter_name_suffix =
# ij_java_parentheses_expression_new_line_after_left_paren = false
# ij_java_parentheses_expression_right_paren_on_new_line = false
# ij_java_place_assignment_sign_on_next_line = false
# ij_java_prefer_longer_names = true
# ij_java_prefer_parameters_wrap = false
# ij_java_preserve_module_imports = true
# ij_java_record_components_wrap = split_into_lines
# ij_java_repeat_annotations =
# ij_java_repeat_synchronized = true
# ij_java_replace_instanceof_and_cast = false
# ij_java_replace_null_check = true
# ij_java_replace_sum_lambda_with_method_ref = true
# ij_java_resource_list_new_line_after_left_paren = false
# ij_java_resource_list_right_paren_on_new_line = false
# ij_java_resource_list_wrap = off
# ij_java_rparen_on_new_line_in_annotation = false
# ij_java_rparen_on_new_line_in_deconstruction_pattern = true
# ij_java_rparen_on_new_line_in_record_header = true
# ij_java_servlet_class_prefix =
# ij_java_servlet_class_suffix =
# ij_java_servlet_dd_prefix =
# ij_java_servlet_dd_suffix =
# ij_java_session_dd_prefix =
# ij_java_session_dd_suffix = EJB
# ij_java_session_eb_prefix =
# ij_java_session_eb_suffix = Bean
# ij_java_session_hi_prefix =
# ij_java_session_hi_suffix = Home
# ij_java_session_lhi_prefix = Local
# ij_java_session_lhi_suffix = Home
# ij_java_session_li_prefix = Local
# ij_java_session_li_suffix =
# ij_java_session_ri_prefix =
# ij_java_session_ri_suffix =
# ij_java_session_si_prefix =
# ij_java_session_si_suffix = Service
# ij_java_space_after_closing_angle_bracket_in_type_argument = false
# ij_java_space_after_colon = true
# ij_java_space_after_comma = true
# ij_java_space_after_comma_in_type_arguments = true
# ij_java_space_after_for_semicolon = true
# ij_java_space_after_quest = true
# ij_java_space_after_type_cast = true
# ij_java_space_before_annotation_array_initializer_left_brace = false
# ij_java_space_before_annotation_parameter_list = false
# ij_java_space_before_array_initializer_left_brace = false
# ij_java_space_before_catch_keyword = true
# ij_java_space_before_catch_left_brace = true
# ij_java_space_before_catch_parentheses = true
# ij_java_space_before_class_left_brace = true
# ij_java_space_before_colon = true
# ij_java_space_before_colon_in_foreach = true
# ij_java_space_before_comma = false
# ij_java_space_before_deconstruction_list = false
# ij_java_space_before_do_left_brace = true
# ij_java_space_before_else_keyword = true
# ij_java_space_before_else_left_brace = true
# ij_java_space_before_finally_keyword = true
# ij_java_space_before_finally_left_brace = true
# ij_java_space_before_for_left_brace = true
# ij_java_space_before_for_parentheses = true
# ij_java_space_before_for_semicolon = false
# ij_java_space_before_if_left_brace = true
# ij_java_space_before_if_parentheses = true
# ij_java_space_before_method_call_parentheses = false
# ij_java_space_before_method_left_brace = true
# ij_java_space_before_method_parentheses = false
# ij_java_space_before_opening_angle_bracket_in_type_parameter = false
# ij_java_space_before_quest = true
# ij_java_space_before_switch_left_brace = true
# ij_java_space_before_switch_parentheses = true
# ij_java_space_before_synchronized_left_brace = true
# ij_java_space_before_synchronized_parentheses = true
# ij_java_space_before_try_left_brace = true
# ij_java_space_before_try_parentheses = true
# ij_java_space_before_type_parameter_list = false
# ij_java_space_before_while_keyword = true
# ij_java_space_before_while_left_brace = true
# ij_java_space_before_while_parentheses = true
# ij_java_space_inside_one_line_enum_braces = false
# ij_java_space_within_empty_array_initializer_braces = false
# ij_java_space_within_empty_method_call_parentheses = false
# ij_java_space_within_empty_method_parentheses = false
# ij_java_spaces_around_additive_operators = true
# ij_java_spaces_around_annotation_eq = true
# ij_java_spaces_around_assignment_operators = true
# ij_java_spaces_around_bitwise_operators = true
# ij_java_spaces_around_equality_operators = true
# ij_java_spaces_around_lambda_arrow = true
# ij_java_spaces_around_logical_operators = true
# ij_java_spaces_around_method_ref_dbl_colon = false
# ij_java_spaces_around_multiplicative_operators = true
# ij_java_spaces_around_relational_operators = true
# ij_java_spaces_around_shift_operators = true
# ij_java_spaces_around_type_bounds_in_type_parameters = true
# ij_java_spaces_around_unary_operator = false
# ij_java_spaces_inside_block_braces_when_body_is_present = false
# ij_java_spaces_within_angle_brackets = false
# ij_java_spaces_within_annotation_parentheses = false
# ij_java_spaces_within_array_initializer_braces = false
# ij_java_spaces_within_braces = false
# ij_java_spaces_within_brackets = false
# ij_java_spaces_within_cast_parentheses = false
# ij_java_spaces_within_catch_parentheses = false
# ij_java_spaces_within_deconstruction_list = false
# ij_java_spaces_within_for_parentheses = false
# ij_java_spaces_within_if_parentheses = false
# ij_java_spaces_within_method_call_parentheses = false
# ij_java_spaces_within_method_parentheses = false
# ij_java_spaces_within_parentheses = false
# ij_java_spaces_within_record_header = false
# ij_java_spaces_within_switch_parentheses = false
# ij_java_spaces_within_synchronized_parentheses = false
# ij_java_spaces_within_try_parentheses = false
# ij_java_spaces_within_while_parentheses = false
# ij_java_special_else_if_treatment = true
# ij_java_static_field_name_prefix =
# ij_java_static_field_name_suffix =
# ij_java_subclass_name_prefix =
# ij_java_subclass_name_suffix = Impl
# ij_java_switch_expressions_wrap = normal
# ij_java_ternary_operation_signs_on_next_line = true
# ij_java_ternary_operation_wrap = normal
# ij_java_test_name_prefix =
# ij_java_test_name_suffix = Test
# ij_java_throws_keyword_wrap = normal
# ij_java_throws_list_wrap = off
# ij_java_use_external_annotations = false
# ij_java_use_fq_class_names = false
# ij_java_use_relative_indents = false
# ij_java_use_single_class_imports = true
# ij_java_variable_annotation_wrap = off
# ij_java_visibility = public
# ij_java_while_brace_force = always
# ij_java_while_on_new_line = false
# ij_java_wrap_comments = true
# ij_java_wrap_first_method_in_call_chain = false
ij_java_wrap_long_lines = false
# ij_java_wrap_semicolon_after_call_chain = false

[{*.ant,*.fxml,*.jhm,*.jnlp,*.jrxml,*.pom,*.rng,*.tld,*.wadl,*.wsdl,*.xml,*.xsd,*.xsl,*.xslt,*.xul}]
# ij_continuation_indent_size = 2
# ij_xml_align_attributes = false
# ij_xml_align_text = false
# ij_xml_attribute_wrap = normal
# ij_xml_block_comment_add_space = false
# ij_xml_block_comment_at_first_column = true
# ij_xml_keep_blank_lines = 2
# ij_xml_keep_indents_on_empty_lines = false
# ij_xml_keep_line_breaks = true
# ij_xml_keep_line_breaks_in_text = true
# ij_xml_keep_whitespaces = false
# ij_xml_keep_whitespaces_around_cdata = preserve
# ij_xml_keep_whitespaces_inside_cdata = false
# ij_xml_line_comment_at_first_column = true
# ij_xml_space_after_tag_name = false
# ij_xml_space_around_equals_in_attribute = false
# ij_xml_space_inside_empty_tag = false
# ij_xml_text_wrap = normal

[{*.properties,application-**.properties,application.properties,bootstrap-**.properties,bootstrap.properties,spring.factories,spring.handlers,spring.schemas}]
# ij_properties_align_group_field_declarations = false
# ij_properties_keep_blank_lines = false
# ij_properties_key_value_delimiter = equals
# ij_properties_spaces_around_key_value_delimiter = false

[{*.toml,Cargo.lock,Cargo.toml.orig,Gopkg.lock,Pipfile,poetry.lock,uv.lock}]
# indent_size = 4
# tab_width = 4
# ij_continuation_indent_size = 8
# ij_toml_keep_indents_on_empty_lines = false

[{*.yaml,*.yml,application-**.y**ml,application.yaml,application.yml,bootstrap-**.y**ml,bootstrap.yaml,bootstrap.yml}]
# ij_yaml_align_values_properties = do_not_align
# ij_yaml_autoinsert_sequence_marker = true
# ij_yaml_block_mapping_on_new_line = false
# ij_yaml_indent_sequence_value = true
# ij_yaml_keep_indents_on_empty_lines = false
# ij_yaml_keep_line_breaks = true
# ij_yaml_line_comment_add_space = false
# ij_yaml_line_comment_add_space_on_reformat = false
# ij_yaml_line_comment_at_first_column = true
# ij_yaml_sequence_on_new_line = false
# ij_yaml_space_before_colon = false
# ij_yaml_spaces_within_braces = true
# ij_yaml_spaces_within_brackets = true
