# 阿里巴巴回调重试和通知配置
shop:
  alibaba:
    # 回调日志清理配置
    callback-log:
      retention-days: 30  # 日志保留天数，默认30天

    # 回调重试配置
    callback-retry:
      enabled: true  # 是否启用重试功能
      max-retry-count: 3  # 最大重试次数
      batch-size: 20  # 每批处理数量
      time-window-hours: 24  # 重试时间窗口（小时）

# 企业微信通知配置
fulfillmen:
  notification:
    wechat:
      enabled: true  # 是否启用企业微信通知
      webhook-url: ${WECHAT_WEBHOOK_URL:}  # 企业微信机器人webhook地址，从环境变量获取
      timeout: 5000  # 请求超时时间（毫秒）

# RestTemplate 配置
spring:
  http:
    client:
      connect-timeout: 5000
      read-timeout: 10000
