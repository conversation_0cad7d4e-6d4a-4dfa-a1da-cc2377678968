/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.dto.order;

import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单维度数据模型
 *
 * <AUTHOR>
 * @date 2025/7/22
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderDwdDTO {

    @Schema(description = "订单项")
    private TzOrderItem orderItem;

    @Schema(description = "采购订单")
    private TzOrderPurchase purchase;

    @Schema(description = "供应商订单")
    private TzOrderSupplier supplier;

    @Schema(description = "订单状态")
    private Integer status;

    @Schema(description = "搜索关键词")
    private String keyword;

    @Schema(description = "SKU ID")
    private Long skuId;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;
}
