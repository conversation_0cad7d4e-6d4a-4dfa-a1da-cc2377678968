/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fulfillmen.shop.domain.entity.enums.AlibabaCallbackLogsProcessStatusEnum;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * alibaba 回调系统日志。
 *
 * <pre>
 * 暂定策略
 * 1. 默认保留 3个月数据, 定时清除数据 >=3个月的数据。
 * 2. 如果过去 24 小时 存在订单处理失败的记录(默认 webhook 自带重试)。将开启自动重试。
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/8/8 09:30
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_alibaba_callback_logs")
public class SysAlibabaCallbackLogs implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * webhook 元数据.
     */
    @TableField(value = "metadata")
    private String metadata;

    /**
     * alibaba 订单 id
     */
    @TableField(value = "order_id")
    private Long orderId;

    /**
     * 事件类型：根据 alibaba 的对应事件类型。
     */
    @TableField(value = "event_type")
    private String eventType;

    /**
     * webhook 接收到的时间戳
     */
    @TableField(value = "received_timestamp")
    private LocalDateTime receivedTimestamp;

    /**
     * 处理状态: =0 处理中，=1 成功，=2 失败
     */
    @TableField(value = "process_status")
    private AlibabaCallbackLogsProcessStatusEnum processStatus;

    /**
     * 处理失败的消息描述
     */
    @TableField(value = "process_failed_msg")
    private String processFailedMsg;

    /**
     * 重试次数 - 定时任务处理定义。
     */
    @TableField(value = "retry_count")
    private Integer retryCount;

    /**
     * 签名
     */
    @TableField(value = "sign")
    private String sign;

    /**
     * 创建日期
     */
    @TableField(value = "gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 业务时间戳（gmtBorn）- 用于消息排序，解决乱序问题
     */
    @TableField(value = "gmt_born_virtual")
    private Long gmtBornVirtual;

}
