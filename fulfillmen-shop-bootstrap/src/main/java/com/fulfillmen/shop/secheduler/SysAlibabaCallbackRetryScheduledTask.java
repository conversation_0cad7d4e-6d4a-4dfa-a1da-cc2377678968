/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.secheduler;

import com.fasterxml.jackson.databind.JsonNode;
import com.fulfillmen.shop.domain.entity.SysAlibabaCallbackLogs;
import com.fulfillmen.shop.manager.core.repository.SysAlibabaCallbackLogsRepository;
import com.fulfillmen.shop.manager.support.alibaba.webhook.service.OrderWebhookService;
import com.fulfillmen.shop.manager.support.notification.service.WeChatNotificationService;
import com.fulfillmen.starter.cache.redisson.util.RedisUtils;
import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.alibaba.enums.CallbackMessageType;
import com.fulfillmen.support.alibaba.enums.LogisticsMessageTypeEnums;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.webhook.MessageEvent;
import com.fulfillmen.support.alibaba.webhook.MessageTypeFactory;
import com.fulfillmen.support.alibaba.webhook.data.GoodsMessage;
import com.fulfillmen.support.alibaba.webhook.data.LogisticsMessage;
import com.fulfillmen.support.alibaba.webhook.data.OrderMessage;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

/**
 * 阿里巴巴回调重试定时任务
 *
 * <pre>
 *     TODO: 2025 年 08 月 13 日 10:28:06 需要等完善部分功能模块后，才能开启处理。  当前的功能尚未完善，还存在一些业务代码逻辑上的缺陷
 *     1. 业务代码里，订单ID 存在多个事件乱序。需要合并后在处理。
 * 功能说明：
 * 1. 定期扫描失败的回调记录（最多重试3次）
 * 2. 重新执行失败的回调处理逻辑
 * 3. 达到最大重试次数后发送企业微信通知
 * 4. 使用分布式锁防止多实例并发执行
 *
 * 配置项：
 * - shop.alibaba.callback-retry.max-retry-count: 最大重试次数（默认3次）
 * - shop.alibaba.callback-retry.batch-size: 每批处理数量（默认20条）
 * - shop.alibaba.callback-retry.time-window-hours: 时间窗口小时数（默认24小时）
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/09 12:10
 * @since 1.0.0
 */
@Slf4j
@Profile({"!dev", "!local"})
@Component
@RequiredArgsConstructor
public class SysAlibabaCallbackRetryScheduledTask {

    private static final String RETRY_LOCK_KEY = "scheduled:alibaba:callback:retry";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final SysAlibabaCallbackLogsRepository callbackLogsRepository;
    private final OrderWebhookService orderWebhookService;
    private final WeChatNotificationService weChatNotificationService;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Value("${shop.alibaba.callback-retry.max-retry-count:3}")
    private int maxRetryCount;
    @Value("${shop.alibaba.callback-retry.batch-size:20}")
    private int batchSize;

    // 移除不需要的依赖，状态一致性检查将在OrderWebhookService中处理
    @Value("${shop.alibaba.callback-retry.time-window-hours:24}")
    private int timeWindowHours;
    @Value("${shop.alibaba.callback-retry.enabled:true}")
    private boolean retryEnabled;

    /**
     * 每30秒执行一次延迟处理任务 - 处理订单最新状态
     */
    @Scheduled(cron = "30 * * * * ?")
    public void scheduledProcessDelayedCallbacks() {
        if (!retryEnabled) {
            log.debug("阿里巴巴回调延迟处理功能已禁用");
            return;
        }

        // 分布式锁，避免多实例并发执行
        boolean locked = RedisUtils.tryLock(RETRY_LOCK_KEY, 600_000L, 0L);
        if (!locked) {
            log.info("阿里巴巴延迟处理任务跳过：未获取到分布式锁");
            return;
        }

        long startTime = System.currentTimeMillis();
        int totalProcessed = 0;
        int totalSkipped = 0;
        int totalFailed = 0;

        try {
            log.info("开始执行阿里巴巴延迟处理任务，延迟时间：30秒，批次大小：{}", batchSize);

            // 查询需要延迟处理的记录 - 每个订单ID的最新记录
            List<SysAlibabaCallbackLogs> delayedLogs = callbackLogsRepository.findLatestUnprocessedLogsByOrderId(30, batchSize);

            if (delayedLogs.isEmpty()) {
                log.debug("没有需要延迟处理的记录");
                return;
            }

            log.info("找到 {} 个订单的最新状态需要处理", delayedLogs.size());

            // 并行处理每个订单的最新状态
            @SuppressWarnings("unchecked")
            CompletableFuture<OrderProcessingResult>[] processingTasks = delayedLogs.stream()
              .map(this::processOrderLatestStatusAsync)
              .toArray(CompletableFuture[]::new);

            // 等待所有处理任务完成
            CompletableFuture.allOf(processingTasks).join();

            // 统计结果
            for (CompletableFuture<OrderProcessingResult> task : processingTasks) {
                try {
                    OrderProcessingResult result = task.get();
                    switch (result.getStatus()) {
                        case SUCCESS -> totalProcessed++;
                        case SKIPPED -> totalSkipped++;
                        case FAILED -> totalFailed++;
                    }
                } catch (Exception e) {
                    totalFailed++;
                    log.error("获取处理结果异常", e);
                }
            }

            long duration = System.currentTimeMillis() - startTime;
            log.info("阿里巴巴延迟处理任务完成：成功处理={}, 跳过={}, 失败={}, 耗时={}ms", totalProcessed, totalSkipped, totalFailed, duration);

        } catch (Exception e) {
            log.error("阿里巴巴延迟处理任务执行异常", e);
            // 发送系统异常通知
            weChatNotificationService.sendSystemErrorNotification("延迟处理任务异常", "阿里巴巴延迟处理定时任务执行异常：" + e.getMessage());
        } finally {
            RedisUtils.unlock(RETRY_LOCK_KEY);
        }
    }

    /**
     * 异步处理订单最新状态
     */
    private CompletableFuture<OrderProcessingResult> processOrderLatestStatusAsync(SysAlibabaCallbackLogs latestLog) {
        return CompletableFuture.supplyAsync(() -> {
            Long orderId = latestLog.getOrderId();
            try {
                log.info("开始处理订单最新状态: orderId={}, logId={}, businessTimestamp={}", orderId, latestLog.getId(), latestLog.getBusinessTimestamp());

                // 重构消息事件并调用业务处理逻辑
                MessageEvent<OrderMessage> messageEvent = reconstructMessageEvent(latestLog);
                OrderMessage orderMessage = messageEvent.getData();
                OrderMessageTypeEnums messageTypeEnum = OrderMessageTypeEnums.fromMessageType(latestLog.getEventType());

                if (messageTypeEnum == null) {
                    log.warn("不支持的消息类型，跳过处理: eventType={}, orderId={}", latestLog.getEventType(), orderId);
                    callbackLogsRepository.markProcessed(latestLog.getId());
                    return new OrderProcessingResult(OrderProcessingStatus.SKIPPED, "不支持的消息类型", orderId);
                }

                // 调用OrderWebhookService处理业务逻辑（包含状态一致性检查）
                orderWebhookService.processOrderWebhook(orderMessage, messageEvent, messageTypeEnum);

                // 标记为已处理
                callbackLogsRepository.markProcessed(latestLog.getId());
                log.info("订单延迟处理完成: orderId={}", orderId);
                return new OrderProcessingResult(OrderProcessingStatus.SUCCESS, "处理成功", orderId);

            } catch (Exception e) {
                log.error("处理订单最新状态失败: orderId={}", orderId, e);
                callbackLogsRepository.markFailed(latestLog.getId(), e.getMessage());
                return new OrderProcessingResult(OrderProcessingStatus.FAILED, e.getMessage(), orderId);
            }
        }, threadPoolTaskExecutor);
    }

    /**
     * 异步处理单个回调记录的重试（保留旧逻辑用于失败重试） 注：暂时未使用，保留备用
     */
    @SuppressWarnings("unused")
    private CompletableFuture<Void> processRetryAsync(SysAlibabaCallbackLogs callbackLog) {
        return CompletableFuture.runAsync(() -> {
            try {
                processRetry(callbackLog);
            } catch (Exception e) {
                log.error("处理回调记录重试异常: logId={}, orderId={}",
                  callbackLog.getId(), callbackLog.getOrderId(), e);
            }
        }, threadPoolTaskExecutor);
    }

    /**
     * 处理单个回调记录的重试
     */
    private void processRetry(SysAlibabaCallbackLogs callbackLog) {
        Long logId = callbackLog.getId();
        Long orderId = callbackLog.getOrderId();
        String eventType = callbackLog.getEventType();
        int currentRetryCount = callbackLog.getRetryCount() != null ? callbackLog.getRetryCount() : 0;

        log.info("开始重试回调记录: logId={}, orderId={}, eventType={}, currentRetryCount={}",
          logId, orderId, eventType, currentRetryCount);

        try {
            // 更新重试次数并标记为处理中
            int newRetryCount = currentRetryCount + 1;
            callbackLogsRepository.updateRetryCount(logId, newRetryCount);

            // 从元数据中重构消息和事件
            MessageEvent<OrderMessage> messageEvent = reconstructMessageEvent(callbackLog);
            OrderMessage orderMessage = messageEvent.getData();
            OrderMessageTypeEnums messageTypeEnum = OrderMessageTypeEnums.fromMessageType(eventType);

            if (messageTypeEnum == null) {
                log.warn("不支持的消息类型，跳过重试: eventType={}, orderId={}", eventType, orderId);
                callbackLogsRepository.markFailed(logId, "不支持的消息类型: " + eventType);
                return;
            }

            // 重新执行订单处理逻辑
            orderWebhookService.processOrderWebhook(orderMessage, messageEvent, messageTypeEnum);

            // 标记成功
            callbackLogsRepository.markSuccess(logId);
            log.info("回调记录重试成功: logId={}, orderId={}, retryCount={}", logId, orderId, newRetryCount);

        } catch (Exception e) {
            log.error("回调记录重试失败: logId={}, orderId={}, error={}", logId, orderId, e.getMessage(), e);

            int newRetryCount = currentRetryCount + 1;
            String failureReason = "重试失败: " + e.getMessage();
            callbackLogsRepository.markFailed(logId, failureReason);

            // 检查是否达到最大重试次数
            if (newRetryCount >= maxRetryCount) {
                log.warn("回调记录达到最大重试次数，发送通知: logId={}, orderId={}, maxRetryCount={}", logId, orderId, maxRetryCount);

                // 发送企业微信通知
                sendRetryFailedNotification(callbackLog, newRetryCount, failureReason);
            }
        }
    }

    /**
     * 从回调日志重构消息事件
     */
    private MessageEvent<OrderMessage> reconstructMessageEvent(SysAlibabaCallbackLogs callbackLog) {
        try {
            // 尝试从元数据中解析原始事件
            String metadata = callbackLog.getMetadata();
            if (metadata != null) {
                // 手动解析 JSON 并重构 MessageEvent
                JsonNode jsonNode = JacksonUtil.convertToBean(metadata, JsonNode.class);
                if (jsonNode != null) {
                    MessageEvent<OrderMessage> event = new MessageEvent<>();

                    // 设置基本字段
                    event.setMsgId("retry-" + callbackLog.getId());
                    event.setReceivedAt(callbackLog.getReceivedTimestamp());

                    if (jsonNode.has("msgId")) {
                        event.setMsgId(jsonNode.get("msgId").asText());
                    }
                    if (jsonNode.has("gmtBorn")) {
                        event.setGmtBorn(jsonNode.get("gmtBorn").asLong());
                    }
                    if (jsonNode.has("userInfo")) {
                        event.setUserInfo(jsonNode.get("userInfo").asText());
                    }
                    if (jsonNode.has("rawData")) {
                        event.setRawData(jsonNode.get("rawData").asText());
                    }

                    // 处理消息类型 - 优先使用日志中保存的事件类型
                    String eventType = callbackLog.getEventType();
                    if (eventType != null) {
                        CallbackMessageType messageType = MessageTypeFactory.fromValue(eventType);
                        event.setType(messageType);
                    } else if (jsonNode.has("type")) {
                        String typeString = jsonNode.get("type").asText();
                        CallbackMessageType messageType = MessageTypeFactory.fromValue(typeString);
                        event.setType(messageType);
                    }

                    // 处理订单数据
                    OrderMessage orderMessage = null;
                    if (event.getType() instanceof OrderMessageTypeEnums) {
                        JsonNode dataNode = jsonNode.get("data");
                        orderMessage = JacksonUtil.convertToBean(dataNode.toString(), OrderMessage.class);
                        event.setData(orderMessage);
                    } else if (event.getType() instanceof LogisticsMessageTypeEnums) {
                        JsonNode dataNode = jsonNode.get("data");
                        LogisticsMessage logisticsMessage = JacksonUtil.convertToBean(dataNode.toString(),
                          LogisticsMessage.class);
                        // event.setData(logisticsMessage);
                    } else if (event.getType() instanceof GoodsMessage) {
                        JsonNode dataNode = jsonNode.get("data");
                        GoodsMessage goodsMessage = JacksonUtil.convertToBean(dataNode.toString(), GoodsMessage.class);
                    }

                    if (jsonNode.has("data")) {
                        JsonNode dataNode = jsonNode.get("data");
                        orderMessage = JacksonUtil.convertToBean(dataNode.toString(), OrderMessage.class);
                    } else {
                        // 如果没有 data 节点，尝试直接解析整个 metadata 为 OrderMessage
                        orderMessage = JacksonUtil.convertToBean(metadata, OrderMessage.class);
                    }

                    // 确保订单ID正确设置
                    if (orderMessage != null && orderMessage.getOrderId() == null && callbackLog.getOrderId() != null) {
                        orderMessage.setOrderId(callbackLog.getOrderId());
                    }

                    return event;
                }
            }
        } catch (Exception e) {
            log.warn("重构消息事件失败: logId={}, metadata={}", callbackLog.getId(),
              callbackLog.getMetadata() != null
                ? callbackLog.getMetadata().substring(0, Math.min(100, callbackLog.getMetadata().length()))
                : "null",
              e);
        }

        // 回退方案：返回基本的事件对象
        log.info("使用回退方案重构消息事件: logId={}, orderId={}, eventType={}",
          callbackLog.getId(), callbackLog.getOrderId(), callbackLog.getEventType());

        MessageEvent<OrderMessage> event = new MessageEvent<>();
        event.setMsgId("retry-" + callbackLog.getId());
        event.setReceivedAt(
          callbackLog.getReceivedTimestamp() != null ? callbackLog.getReceivedTimestamp() : LocalDateTime.now());

        // 使用 MessageTypeFactory 设置消息类型
        if (callbackLog.getEventType() != null) {
            CallbackMessageType messageType = MessageTypeFactory.fromValue(callbackLog.getEventType());
            event.setType(messageType);
        }

        // 构建基本的订单消息
        OrderMessage orderMessage = new OrderMessage();
        if (callbackLog.getOrderId() != null) {
            orderMessage.setOrderId(callbackLog.getOrderId());
        }
        event.setData(orderMessage);

        return event;
    }

    /**
     * 发送重试失败通知
     */
    private void sendRetryFailedNotification(SysAlibabaCallbackLogs callbackLog, int retryCount, String failureReason) {
        try {
            String lastFailedTime = callbackLog.getGmtModified() != null
              ? callbackLog.getGmtModified().format(FORMATTER)
              : "未知";

            weChatNotificationService.sendCallbackRetryFailedNotification(
              callbackLog.getOrderId(),
              callbackLog.getEventType(),
              retryCount,
              failureReason,
              lastFailedTime);
        } catch (Exception e) {
            log.error("发送重试失败通知异常: logId={}", callbackLog.getId(), e);
        }
    }

    /**
     * 订单处理结果枚举
     */
    public enum OrderProcessingStatus {
        SUCCESS, // 成功处理
        SKIPPED, // 跳过（状态一致）
        FAILED // 处理失败
    }

    /**
     * 订单处理结果
     */
    public static class OrderProcessingResult {

        private final OrderProcessingStatus status;
        private final String message;
        private final Long orderId;

        public OrderProcessingResult(OrderProcessingStatus status, String message, Long orderId) {
            this.status = status;
            this.message = message;
            this.orderId = orderId;
        }

        public OrderProcessingStatus getStatus() {
            return status;
        }

        public String getMessage() {
            return message;
        }

        public Long getOrderId() {
            return orderId;
        }
    }

    // 移除重复的状态一致性检查逻辑，已移至OrderWebhookServiceImpl
}
